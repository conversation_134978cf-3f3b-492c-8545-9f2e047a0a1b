# 浮光壁垒线下支付功能部署检查清单

## 数据库更新 ✅

### 1. 执行SQL脚本
- [ ] 执行 `fuguang-api/sql/ry_20250522.sql`（已更新，包含商家申请表扩展）
- [ ] 执行 `fuguang-api/sql/offline_payment.sql`（新增线下支付相关表）
- [ ] 执行测试数据脚本 `fuguang-api/test_offline_payment.sql`（可选）

### 2. 验证表结构
- [ ] 确认 `merchant_application` 表已添加 `alipay_account` 和 `alipay_name` 字段
- [ ] 确认 `offline_payment` 表已创建
- [ ] 确认 `merchant_qrcode` 表已创建
- [ ] 确认菜单权限数据已插入 `sys_menu` 表
- [ ] 确认数据字典已插入 `sys_dict_type` 和 `sys_dict_data` 表

## 后端代码部署 ✅

### 1. 实体类和Mapper
- [ ] `OfflinePayment.java` - 线下支付订单实体
- [ ] `MerchantQrcode.java` - 商家二维码实体
- [ ] `MerchantApplication.java` - 已更新，添加支付宝账号字段
- [ ] `OfflinePaymentMapper.java` 和对应的XML文件
- [ ] `MerchantQrcodeMapper.java` 和对应的XML文件
- [ ] `MerchantApplicationMapper.xml` - 已更新

### 2. 服务层
- [ ] `IOfflinePaymentService.java` - 线下支付服务接口
- [ ] `OfflinePaymentServiceImpl.java` - 线下支付服务实现
- [ ] 确认 `IAlipayService` 中有 `verifyCallback` 和 `transfer` 方法

### 3. 控制器
- [ ] `OfflinePaymentController.java` - 管理后台支付订单控制器
- [ ] `MerchantQrcodeController.java` - 管理后台二维码控制器
- [ ] `AppPaymentController.java` - 已更新，添加线下支付接口

### 4. APP接口验证
- [ ] `GET /app/payment/offline/merchant/{merchantId}/qrcode` - 生成商家二维码
- [ ] `GET /app/payment/offline/merchant/{merchantId}/qrcode/info` - 获取商家二维码
- [ ] `POST /app/payment/offline/create` - 创建线下支付订单
- [ ] `POST /app/payment/offline/alipay/notify` - 支付宝回调处理
- [ ] `GET /app/payment/offline/query/{orderNo}` - 查询支付状态
- [ ] `POST /app/payment/offline/test/success` - 测试接口

## 前端代码部署 ✅

### 1. 管理后台页面
- [ ] `fuguang-web/src/views/fuguang/offlinePayment/index.vue` - 支付订单管理
- [ ] `fuguang-web/src/views/fuguang/merchantQrcode/index.vue` - 商家二维码管理
- [ ] `fuguang-web/src/views/fuguang/offlinePayment/statistics.vue` - 支付统计
- [ ] `fuguang-web/src/views/fuguang/merchant/index.vue` - 已更新，添加生成二维码功能

### 2. API接口文件
- [ ] `fuguang-web/src/api/fuguang/offlinePayment.js` - 支付订单API
- [ ] `fuguang-web/src/api/fuguang/merchantQrcode.js` - 商家二维码API

### 3. 路由配置
- [ ] 确认管理后台路由已配置
- [ ] 确认菜单权限已生效

## 配置检查 ⚠️

### 1. 支付宝配置
- [ ] 确认 `application.yml` 中支付宝配置正确
- [ ] 确认支付宝应用ID、私钥、公钥配置
- [ ] 确认回调地址配置：`https://your-domain.com/app/payment/offline/alipay/notify`
- [ ] 确认域名和HTTPS证书配置

### 2. 二维码配置
- [ ] 确认二维码内容基础URL配置
- [ ] 确认前端支付页面路径配置

### 3. 转账配置
- [ ] 确认支付宝转账接口配置
- [ ] 确认平台手续费率配置（默认0.6%）

## 权限配置 ⚠️

### 1. 菜单权限
- [ ] 确认"线下支付管理"目录菜单已显示
- [ ] 确认"支付订单管理"菜单已显示
- [ ] 确认"二维码管理"菜单已显示
- [ ] 确认"支付统计"菜单已显示

### 2. 角色权限分配
- [ ] 为管理员角色分配相关权限
- [ ] 为财务角色分配查询和转账权限
- [ ] 为客服角色分配查询权限

### 3. 数据字典
- [ ] 确认支付状态字典已生效
- [ ] 确认支付方式字典已生效
- [ ] 确认转账状态字典已生效

## 功能测试 ⚠️

### 1. 商家申请流程测试
- [ ] 商家提交申请时能填写支付宝账号
- [ ] 管理员能查看商家支付宝账号信息
- [ ] 审核通过后能生成二维码

### 2. 二维码管理测试
- [ ] 能成功生成商家二维码
- [ ] 能查看二维码列表和详情
- [ ] 能启用/禁用二维码
- [ ] 能导出二维码数据

### 3. 支付流程测试
- [ ] 扫描二维码能跳转到支付页面
- [ ] 能创建线下支付订单
- [ ] 支付成功后能收到回调
- [ ] 能自动转账给商家
- [ ] 能查询支付状态

### 4. 管理后台测试
- [ ] 能查看支付订单列表
- [ ] 能查看订单详情
- [ ] 能重试转账失败的订单
- [ ] 能批量操作订单
- [ ] 能导出订单数据
- [ ] 统计页面数据正确显示

### 5. 异常处理测试
- [ ] 转账失败时能正确处理
- [ ] 重复支付能正确处理
- [ ] 签名验证失败能正确处理
- [ ] 网络异常能正确处理

## 安全检查 ⚠️

### 1. 支付安全
- [ ] 支付宝回调签名验证正常
- [ ] 订单金额验证正常
- [ ] 防重复支付机制正常

### 2. 数据安全
- [ ] 敏感数据加密存储
- [ ] 支付宝账号信息安全
- [ ] 转账记录完整

### 3. 接口安全
- [ ] APP接口权限验证
- [ ] 管理后台权限验证
- [ ] 参数校验正常

## 监控和日志 ⚠️

### 1. 日志配置
- [ ] 支付流程关键节点有日志记录
- [ ] 转账操作有详细日志
- [ ] 异常情况有错误日志

### 2. 监控告警
- [ ] 转账失败告警机制
- [ ] 支付异常告警机制
- [ ] 系统异常告警机制

## 文档和培训 ✅

### 1. 技术文档
- [ ] 线下支付功能说明文档已完成
- [ ] 管理后台功能说明文档已完成
- [ ] API接口文档已完成

### 2. 操作手册
- [ ] 管理员操作手册
- [ ] 商家使用手册
- [ ] 异常处理手册

### 3. 培训材料
- [ ] 功能培训PPT
- [ ] 操作演示视频
- [ ] 常见问题FAQ

## 上线准备 ⚠️

### 1. 环境准备
- [ ] 生产环境数据库更新
- [ ] 生产环境代码部署
- [ ] 生产环境配置更新

### 2. 备份准备
- [ ] 数据库备份
- [ ] 代码备份
- [ ] 配置文件备份

### 3. 回滚准备
- [ ] 回滚方案制定
- [ ] 回滚脚本准备
- [ ] 应急联系人确定

## 上线后验证 ⚠️

### 1. 功能验证
- [ ] 完整支付流程验证
- [ ] 管理后台功能验证
- [ ] 数据统计验证

### 2. 性能验证
- [ ] 支付接口响应时间
- [ ] 管理后台页面加载速度
- [ ] 数据库查询性能

### 3. 稳定性验证
- [ ] 连续运行24小时无异常
- [ ] 高并发情况下稳定性
- [ ] 异常恢复能力

---

## 检查说明

- ✅ 表示已完成的项目
- ⚠️ 表示需要根据实际环境配置和测试的项目
- [ ] 表示待检查的具体项目

请按照此清单逐项检查，确保所有功能正常运行后再正式上线。
