-- ----------------------------
-- 商城系统升级脚本 - 添加商品规格和物流功能
-- ----------------------------

-- 1. 修改购物车表，添加规格ID字段
ALTER TABLE `mall_cart` ADD COLUMN `spec_id` bigint(20) DEFAULT NULL COMMENT '商品规格ID' AFTER `product_id`;
ALTER TABLE `mall_cart` ADD KEY `idx_spec_id` (`spec_id`);
-- 删除原有的唯一索引，重新创建包含规格ID的唯一索引
ALTER TABLE `mall_cart` DROP INDEX `uk_user_product`;
ALTER TABLE `mall_cart` ADD UNIQUE KEY `uk_user_product_spec` (`user_id`,`product_id`,`spec_id`);

-- 2. 修改订单表，添加地址ID字段
ALTER TABLE `mall_order` ADD COLUMN `address_id` bigint(20) DEFAULT NULL COMMENT '收货地址ID' AFTER `user_id`;
ALTER TABLE `mall_order` ADD KEY `idx_address_id` (`address_id`);

-- 3. 修改订单详情表，添加规格相关字段
ALTER TABLE `mall_order_item` ADD COLUMN `spec_id` bigint(20) DEFAULT NULL COMMENT '商品规格ID' AFTER `product_id`;
ALTER TABLE `mall_order_item` ADD COLUMN `spec_name` varchar(100) DEFAULT '' COMMENT '规格名称' AFTER `product_name`;
ALTER TABLE `mall_order_item` ADD COLUMN `spec_image` varchar(200) DEFAULT '' COMMENT '规格图片' AFTER `product_image`;
ALTER TABLE `mall_order_item` ADD KEY `idx_spec_id` (`spec_id`);

-- 4. 创建商品规格表
CREATE TABLE IF NOT EXISTS `mall_product_spec` (
  `spec_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '规格ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `spec_name` varchar(100) NOT NULL COMMENT '规格名称',
  `spec_image` varchar(500) DEFAULT '' COMMENT '规格图片',
  `sale_price` decimal(10,2) NOT NULL COMMENT '售卖价格',
  `supply_price` decimal(10,2) DEFAULT 0.00 COMMENT '供货价格',
  `stock_quantity` int(11) DEFAULT 0 COMMENT '库存数量',
  `spec_status` char(1) DEFAULT '0' COMMENT '规格状态（0上架 1下架）',
  `sort_order` int(4) DEFAULT 0 COMMENT '显示顺序',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`spec_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_spec_status` (`spec_status`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 COMMENT='商品规格表';

-- 5. 创建订单物流表
CREATE TABLE IF NOT EXISTS `mall_order_logistics` (
  `logistics_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '物流ID',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `order_no` varchar(32) NOT NULL COMMENT '订单号',
  `logistics_type` char(1) DEFAULT '1' COMMENT '物流类型（1发货 2退货）',
  `logistics_company` varchar(50) DEFAULT '' COMMENT '物流公司',
  `logistics_no` varchar(50) DEFAULT '' COMMENT '物流单号',
  `logistics_status` char(1) DEFAULT '0' COMMENT '物流状态（0待发货 1已发货 2运输中 3已签收 4异常）',
  `sender_name` varchar(50) DEFAULT '' COMMENT '发件人姓名',
  `sender_phone` varchar(20) DEFAULT '' COMMENT '发件人电话',
  `sender_address` varchar(200) DEFAULT '' COMMENT '发件地址',
  `receiver_name` varchar(50) DEFAULT '' COMMENT '收件人姓名',
  `receiver_phone` varchar(20) DEFAULT '' COMMENT '收件人电话',
  `receiver_address` varchar(200) DEFAULT '' COMMENT '收件地址',
  `send_time` datetime DEFAULT NULL COMMENT '发货时间',
  `receive_time` datetime DEFAULT NULL COMMENT '签收时间',
  `logistics_info` text COMMENT '物流跟踪信息（JSON格式）',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`logistics_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_logistics_no` (`logistics_no`),
  KEY `idx_logistics_status` (`logistics_status`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 COMMENT='订单物流表';

-- 6. 添加菜单权限
-- 商品规格管理菜单
INSERT INTO sys_menu VALUES('3030', '商品规格', '3000', '4', 'spec', 'mall/spec/index', '', '', 1, 0, 'C', '0', '0', 'mall:spec:list', 'component', 'admin', NOW(), '', NULL, '商品规格管理菜单');
INSERT INTO sys_menu VALUES('3031', '规格查询', '3030', '1', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:spec:query', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3032', '规格新增', '3030', '2', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:spec:add', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3033', '规格修改', '3030', '3', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:spec:edit', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3034', '规格删除', '3030', '4', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:spec:remove', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3035', '规格导出', '3030', '5', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:spec:export', '#', 'admin', NOW(), '', NULL, '');

-- 物流管理菜单
INSERT INTO sys_menu VALUES('3040', '物流管理', '3000', '5', 'logistics', 'mall/logistics/index', '', '', 1, 0, 'C', '0', '0', 'mall:logistics:list', 'guide', 'admin', NOW(), '', NULL, '物流管理菜单');
INSERT INTO sys_menu VALUES('3041', '物流查询', '3040', '1', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:logistics:query', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3042', '物流新增', '3040', '2', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:logistics:add', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3043', '物流修改', '3040', '3', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:logistics:edit', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3044', '物流删除', '3040', '4', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:logistics:remove', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3045', '物流导出', '3040', '5', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:logistics:export', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3046', '创建发货', '3040', '6', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:logistics:delivery', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3047', '状态更新', '3040', '7', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:logistics:status', '#', 'admin', NOW(), '', NULL, '');

-- 给管理员角色分配新菜单权限
INSERT INTO sys_role_menu VALUES ('1', '3030');
INSERT INTO sys_role_menu VALUES ('1', '3031');
INSERT INTO sys_role_menu VALUES ('1', '3032');
INSERT INTO sys_role_menu VALUES ('1', '3033');
INSERT INTO sys_role_menu VALUES ('1', '3034');
INSERT INTO sys_role_menu VALUES ('1', '3035');
INSERT INTO sys_role_menu VALUES ('1', '3040');
INSERT INTO sys_role_menu VALUES ('1', '3041');
INSERT INTO sys_role_menu VALUES ('1', '3042');
INSERT INTO sys_role_menu VALUES ('1', '3043');
INSERT INTO sys_role_menu VALUES ('1', '3044');
INSERT INTO sys_role_menu VALUES ('1', '3045');
INSERT INTO sys_role_menu VALUES ('1', '3046');
INSERT INTO sys_role_menu VALUES ('1', '3047');
