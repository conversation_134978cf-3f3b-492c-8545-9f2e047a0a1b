-- 任务支付记录表
CREATE TABLE `task_payment` (
  `payment_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '支付记录ID',
  `task_id` bigint(20) NOT NULL COMMENT '任务ID',
  `task_title` varchar(200) NOT NULL COMMENT '任务标题',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `user_name` varchar(50) NOT NULL COMMENT '用户昵称',
  `order_no` varchar(64) NOT NULL COMMENT '支付订单号',
  `pay_amount` decimal(10,2) NOT NULL COMMENT '支付金额',
  `pay_type` char(1) NOT NULL DEFAULT '1' COMMENT '支付方式（1支付宝 2微信 3余额）',
  `pay_status` char(1) NOT NULL DEFAULT '0' COMMENT '支付状态（0待支付 1支付成功 2支付失败 3已退款）',
  `trade_no` varchar(64) DEFAULT NULL COMMENT '第三方交易号',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `notify_time` datetime DEFAULT NULL COMMENT '通知时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`payment_id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_pay_status` (`pay_status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务支付记录表';

-- 提现记录表
CREATE TABLE `withdraw_record` (
  `withdraw_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '提现记录ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `user_name` varchar(50) NOT NULL COMMENT '用户昵称',
  `withdraw_no` varchar(64) NOT NULL COMMENT '提现单号',
  `withdraw_amount` decimal(10,2) NOT NULL COMMENT '提现金额',
  `withdraw_fee` decimal(10,2) DEFAULT '0.00' COMMENT '提现手续费',
  `actual_amount` decimal(10,2) NOT NULL COMMENT '实际到账金额',
  `withdraw_type` char(1) NOT NULL DEFAULT '1' COMMENT '提现方式（1支付宝 2微信 3银行卡）',
  `withdraw_status` char(1) NOT NULL DEFAULT '0' COMMENT '提现状态（0申请中 1处理中 2提现成功 3提现失败）',
  `payee_account` varchar(100) NOT NULL COMMENT '收款账户',
  `payee_name` varchar(50) NOT NULL COMMENT '收款人姓名',
  `trade_no` varchar(64) DEFAULT NULL COMMENT '第三方交易号',
  `fail_reason` varchar(200) DEFAULT NULL COMMENT '失败原因',
  `apply_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
  `process_time` datetime DEFAULT NULL COMMENT '处理时间',
  `finish_time` datetime DEFAULT NULL COMMENT '完成时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`withdraw_id`),
  UNIQUE KEY `uk_withdraw_no` (`withdraw_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_withdraw_status` (`withdraw_status`),
  KEY `idx_apply_time` (`apply_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提现记录表';

-- 用户余额表
CREATE TABLE `user_balance` (
  `balance_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '余额记录ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `user_name` varchar(50) NOT NULL COMMENT '用户昵称',
  `total_balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总余额',
  `available_balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '可用余额',
  `frozen_balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '冻结余额',
  `total_income` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '累计收入',
  `total_withdraw` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '累计提现',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`balance_id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户余额表';

-- 余额变动记录表
CREATE TABLE `balance_record` (
  `record_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `user_name` varchar(50) NOT NULL COMMENT '用户昵称',
  `change_type` char(1) NOT NULL COMMENT '变动类型（1收入 2支出）',
  `income_type` char(1) DEFAULT NULL COMMENT '收入类型（1任务佣金 2推荐奖励 3其他收入）',
  `change_amount` decimal(10,2) NOT NULL COMMENT '变动金额',
  `balance_before` decimal(10,2) NOT NULL COMMENT '变动前余额',
  `balance_after` decimal(10,2) NOT NULL COMMENT '变动后余额',
  `business_type` varchar(20) NOT NULL COMMENT '业务类型（task_commission任务佣金 withdraw提现 refund退款等）',
  `business_id` bigint(20) DEFAULT NULL COMMENT '关联业务ID',
  `business_no` varchar(64) DEFAULT NULL COMMENT '关联业务单号',
  `description` varchar(200) NOT NULL COMMENT '变动说明',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`record_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_change_type` (`change_type`),
  KEY `idx_income_type` (`income_type`),
  KEY `idx_business_type` (`business_type`),
  KEY `idx_business_id` (`business_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='余额变动记录表';

-- 佣金账单表
CREATE TABLE `commission_bill` (
  `bill_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '账单ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `user_name` varchar(50) NOT NULL COMMENT '用户昵称',
  `bill_year` int(4) NOT NULL COMMENT '账单年份',
  `bill_month` int(2) NOT NULL COMMENT '账单月份',
  `total_income` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总收入',
  `task_commission` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '任务佣金收入',
  `recommend_reward` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '推荐奖励收入',
  `other_income` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '其他收入',
  `total_withdraw` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总提现',
  `withdraw_count` int(11) NOT NULL DEFAULT '0' COMMENT '提现次数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`bill_id`),
  UNIQUE KEY `uk_user_month` (`user_id`, `bill_year`, `bill_month`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_bill_date` (`bill_year`, `bill_month`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='佣金账单表';
