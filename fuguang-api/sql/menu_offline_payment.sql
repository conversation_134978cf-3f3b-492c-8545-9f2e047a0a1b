-- 线下支付管理菜单配置
-- 执行前请先检查菜单ID是否冲突

-- 删除可能存在的旧菜单（可选）
DELETE FROM sys_menu WHERE menu_id BETWEEN 2030 AND 2044;

-- 线下支付管理目录菜单
INSERT INTO sys_menu VALUES('2030', '线下支付管理', '2000', '4', 'offlinePayment', '', '', '', 1, 0, 'M', '0', '0', '', 'money', 'admin', sysdate(), '', null, '线下支付管理目录');

-- 线下支付订单管理菜单
INSERT INTO sys_menu VALUES('2031', '支付订单管理', '2030', '1', 'offlinePayment', 'fuguang/offlinePayment/index', '', '', 1, 0, 'C', '0', '0', 'fuguang:offlinePayment:list', 'shopping', 'admin', sysdate(), '', null, '线下支付订单管理菜单');
INSERT INTO sys_menu VALUES('2032', '订单查询', '2031', '1', '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:offlinePayment:query', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES('2033', '订单新增', '2031', '2', '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:offlinePayment:add', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES('2034', '订单修改', '2031', '3', '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:offlinePayment:edit', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES('2035', '订单删除', '2031', '4', '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:offlinePayment:remove', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES('2036', '订单导出', '2031', '5', '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:offlinePayment:export', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES('2037', '重新转账', '2031', '6', '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:offlinePayment:transfer', '#', 'admin', sysdate(), '', null, '');

-- 商家二维码管理菜单
INSERT INTO sys_menu VALUES('2038', '二维码管理', '2030', '2', 'merchantQrcode', 'fuguang/merchantQrcode/index', '', '', 1, 0, 'C', '0', '0', 'fuguang:merchantQrcode:list', 'qrcode', 'admin', sysdate(), '', null, '商家二维码管理菜单');
INSERT INTO sys_menu VALUES('2039', '二维码查询', '2038', '1', '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:merchantQrcode:query', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES('2040', '二维码新增', '2038', '2', '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:merchantQrcode:add', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES('2041', '二维码修改', '2038', '3', '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:merchantQrcode:edit', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES('2042', '二维码删除', '2038', '4', '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:merchantQrcode:remove', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES('2043', '二维码导出', '2038', '5', '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:merchantQrcode:export', '#', 'admin', sysdate(), '', null, '');

-- 线下支付统计页面
INSERT INTO sys_menu VALUES('2044', '支付统计', '2030', '3', 'statistics', 'fuguang/offlinePayment/statistics', '', '', 1, 0, 'C', '0', '0', 'fuguang:offlinePayment:list', 'chart', 'admin', sysdate(), '', null, '线下支付统计页面');

-- 线下支付相关数据字典
INSERT INTO sys_dict_type VALUES(100, '支付状态', 'pay_status', '0', 'admin', sysdate(), '', null, '支付状态列表');
INSERT INTO sys_dict_type VALUES(101, '支付方式', 'pay_type', '0', 'admin', sysdate(), '', null, '支付方式列表');
INSERT INTO sys_dict_type VALUES(102, '转账状态', 'transfer_status', '0', 'admin', sysdate(), '', null, '转账状态列表');

INSERT INTO sys_dict_data VALUES(100, 1, '待支付', '0', 'pay_status', '', 'warning', 'N', '0', 'admin', sysdate(), '', null, '待支付状态');
INSERT INTO sys_dict_data VALUES(101, 2, '支付成功', '1', 'pay_status', '', 'success', 'N', '0', 'admin', sysdate(), '', null, '支付成功状态');
INSERT INTO sys_dict_data VALUES(102, 3, '支付失败', '2', 'pay_status', '', 'danger', 'N', '0', 'admin', sysdate(), '', null, '支付失败状态');
INSERT INTO sys_dict_data VALUES(103, 4, '已退款', '3', 'pay_status', '', 'info', 'N', '0', 'admin', sysdate(), '', null, '已退款状态');

INSERT INTO sys_dict_data VALUES(104, 1, '支付宝', '1', 'pay_type', '', 'primary', 'N', '0', 'admin', sysdate(), '', null, '支付宝支付');
INSERT INTO sys_dict_data VALUES(105, 2, '微信', '2', 'pay_type', '', 'success', 'N', '0', 'admin', sysdate(), '', null, '微信支付');
INSERT INTO sys_dict_data VALUES(106, 3, '余额', '3', 'pay_type', '', 'warning', 'N', '0', 'admin', sysdate(), '', null, '余额支付');

INSERT INTO sys_dict_data VALUES(107, 1, '未转账', '0', 'transfer_status', '', 'info', 'N', '0', 'admin', sysdate(), '', null, '未转账状态');
INSERT INTO sys_dict_data VALUES(108, 2, '转账中', '1', 'transfer_status', '', 'warning', 'N', '0', 'admin', sysdate(), '', null, '转账中状态');
INSERT INTO sys_dict_data VALUES(109, 3, '转账成功', '2', 'transfer_status', '', 'success', 'N', '0', 'admin', sysdate(), '', null, '转账成功状态');
INSERT INTO sys_dict_data VALUES(110, 4, '转账失败', '3', 'transfer_status', '', 'danger', 'N', '0', 'admin', sysdate(), '', null, '转账失败状态');

-- 为admin用户角色分配菜单权限
INSERT INTO sys_role_menu SELECT '1', menu_id FROM sys_menu WHERE menu_id BETWEEN 2030 AND 2044;

-- 验证菜单是否插入成功
SELECT menu_id, menu_name, parent_id, path, component, perms FROM sys_menu WHERE menu_id BETWEEN 2030 AND 2044 ORDER BY menu_id;
