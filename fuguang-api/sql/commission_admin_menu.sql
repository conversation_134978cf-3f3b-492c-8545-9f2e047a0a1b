-- 佣金管理系统后台菜单权限配置
-- 执行前请确保已经执行了 commission_system.sql

-- 插入佣金管理主菜单
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(2030, '佣金管理', 0, 6, 'commission', NULL, '', 1, 0, 'M', '0', '0', '', 'money', 'admin', NOW(), '', NULL, '佣金管理目录');

-- 插入用户余额管理菜单
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(2031, '用户余额', 2030, 1, 'balance', 'fuguang/balance/index', '', 1, 0, 'C', '0', '0', 'fuguang:balance:list', 'wallet', 'admin', NOW(), '', NULL, '用户余额菜单');

-- 插入用户余额管理操作权限
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(2032, '用户余额查询', 2031, 1, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:balance:query', '#', 'admin', NOW(), '', NULL, ''),
(2033, '用户余额新增', 2031, 2, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:balance:add', '#', 'admin', NOW(), '', NULL, ''),
(2034, '用户余额修改', 2031, 3, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:balance:edit', '#', 'admin', NOW(), '', NULL, ''),
(2035, '用户余额删除', 2031, 4, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:balance:remove', '#', 'admin', NOW(), '', NULL, ''),
(2036, '用户余额导出', 2031, 5, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:balance:export', '#', 'admin', NOW(), '', NULL, ''),
(2037, '余额调整', 2031, 6, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:balance:adjust', '#', 'admin', NOW(), '', NULL, ''),
(2038, '余额冻结', 2031, 7, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:balance:freeze', '#', 'admin', NOW(), '', NULL, ''),
(2039, '余额解冻', 2031, 8, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:balance:unfreeze', '#', 'admin', NOW(), '', NULL, ''),
(2040, '余额统计', 2031, 9, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:balance:statistics', '#', 'admin', NOW(), '', NULL, '');

-- 插入佣金账单管理菜单
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(2041, '佣金账单', 2030, 2, 'commission', 'fuguang/commission/index', '', 1, 0, 'C', '0', '0', 'fuguang:commission:list', 'documentation', 'admin', NOW(), '', NULL, '佣金账单菜单');

-- 插入佣金账单管理操作权限
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(2042, '佣金账单查询', 2041, 1, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:commission:query', '#', 'admin', NOW(), '', NULL, ''),
(2043, '佣金账单新增', 2041, 2, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:commission:add', '#', 'admin', NOW(), '', NULL, ''),
(2044, '佣金账单修改', 2041, 3, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:commission:edit', '#', 'admin', NOW(), '', NULL, ''),
(2045, '佣金账单删除', 2041, 4, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:commission:remove', '#', 'admin', NOW(), '', NULL, ''),
(2046, '佣金账单导出', 2041, 5, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:commission:export', '#', 'admin', NOW(), '', NULL, ''),
(2047, '佣金统计', 2041, 6, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:commission:statistics', '#', 'admin', NOW(), '', NULL, '');

-- 插入提现管理菜单
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(2048, '提现管理', 2030, 3, 'withdraw', 'fuguang/withdraw/index', '', 1, 0, 'C', '0', '0', 'fuguang:withdraw:list', 'money', 'admin', NOW(), '', NULL, '提现管理菜单');

-- 插入提现管理操作权限
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(2049, '提现记录查询', 2048, 1, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:withdraw:query', '#', 'admin', NOW(), '', NULL, ''),
(2050, '提现记录导出', 2048, 2, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:withdraw:export', '#', 'admin', NOW(), '', NULL, ''),
(2051, '提现审核', 2048, 3, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:withdraw:audit', '#', 'admin', NOW(), '', NULL, ''),
(2052, '提现统计', 2048, 4, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:withdraw:statistics', '#', 'admin', NOW(), '', NULL, '');

-- 插入余额变动记录管理菜单
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(2053, '余额记录', 2030, 4, 'balanceRecord', 'fuguang/balanceRecord/index', '', 1, 0, 'C', '0', '0', 'fuguang:balanceRecord:list', 'list', 'admin', NOW(), '', NULL, '余额变动记录菜单');

-- 插入余额变动记录管理操作权限
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(2054, '余额记录查询', 2053, 1, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:balanceRecord:query', '#', 'admin', NOW(), '', NULL, ''),
(2055, '余额记录新增', 2053, 2, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:balanceRecord:add', '#', 'admin', NOW(), '', NULL, ''),
(2056, '余额记录修改', 2053, 3, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:balanceRecord:edit', '#', 'admin', NOW(), '', NULL, ''),
(2057, '余额记录删除', 2053, 4, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:balanceRecord:remove', '#', 'admin', NOW(), '', NULL, ''),
(2058, '余额记录导出', 2053, 5, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:balanceRecord:export', '#', 'admin', NOW(), '', NULL, ''),
(2059, '余额记录统计', 2053, 6, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:balanceRecord:statistics', '#', 'admin', NOW(), '', NULL, '');

-- 插入佣金统计菜单
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(2060, '佣金统计', 2030, 5, 'statistics', 'fuguang/statistics/index', '', 1, 0, 'C', '0', '0', 'fuguang:statistics:overview', 'chart', 'admin', NOW(), '', NULL, '佣金统计菜单');

-- 插入佣金统计操作权限
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
(2061, '系统概览', 2060, 1, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:statistics:overview', '#', 'admin', NOW(), '', NULL, ''),
(2062, '收支趋势', 2060, 2, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:statistics:trend', '#', 'admin', NOW(), '', NULL, ''),
(2063, '余额分布', 2060, 3, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:statistics:balance-distribution', '#', 'admin', NOW(), '', NULL, ''),
(2064, '收入分布', 2060, 4, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:statistics:income-distribution', '#', 'admin', NOW(), '', NULL, ''),
(2065, '提现渠道', 2060, 5, '', '', '', 1, 0, 'F', '0', '0', 'fuguang:statistics:withdraw-channel', '#', 'admin', NOW(), '', NULL, '');

-- 为管理员角色分配佣金管理权限
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) 
SELECT 1, menu_id FROM `sys_menu` WHERE menu_id BETWEEN 2030 AND 2065;

-- 显示创建结果
SELECT '佣金管理系统菜单权限创建完成' as message;
SELECT 
    menu_id as '菜单ID',
    menu_name as '菜单名称',
    parent_id as '父菜单ID',
    menu_type as '菜单类型',
    perms as '权限标识'
FROM sys_menu 
WHERE menu_id BETWEEN 2030 AND 2065
ORDER BY menu_id;
