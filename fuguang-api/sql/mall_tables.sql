-- ----------------------------
-- 商城相关数据库表结构
-- ----------------------------

-- ----------------------------
-- 1、商品分类表
-- ----------------------------
DROP TABLE IF EXISTS `mall_category`;
CREATE TABLE `mall_category` (
  `category_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `parent_id` bigint(20) DEFAULT 0 COMMENT '父分类ID',
  `category_name` varchar(50) NOT NULL COMMENT '分类名称',
  `category_icon` varchar(200) DEFAULT '' COMMENT '分类图标',
  `sort_order` int(4) DEFAULT 0 COMMENT '显示顺序',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`category_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 COMMENT='商品分类表';

-- ----------------------------
-- 2、商品信息表
-- ----------------------------
DROP TABLE IF EXISTS `mall_product`;
CREATE TABLE `mall_product` (
  `product_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '商品ID',
  `category_id` bigint(20) NOT NULL COMMENT '分类ID',
  `product_name` varchar(100) NOT NULL COMMENT '商品名称',
  `product_desc` text COMMENT '商品描述',
  `product_image` varchar(500) DEFAULT '' COMMENT '商品主图',
  `product_images` text COMMENT '商品图片集合（JSON格式）',
  `original_price` decimal(10,2) DEFAULT 0.00 COMMENT '原价',
  `sale_price` decimal(10,2) NOT NULL COMMENT '销售价格',
  `stock_quantity` int(11) DEFAULT 0 COMMENT '库存数量',
  `sales_count` int(11) DEFAULT 0 COMMENT '销量',
  `product_status` char(1) DEFAULT '0' COMMENT '商品状态（0上架 1下架）',
  `is_hot` char(1) DEFAULT '0' COMMENT '是否热门（0否 1是）',
  `is_new` char(1) DEFAULT '0' COMMENT '是否新品（0否 1是）',
  `is_recommend` char(1) DEFAULT '0' COMMENT '是否推荐（0否 1是）',
  `sort_order` int(4) DEFAULT 0 COMMENT '显示顺序',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`product_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_product_status` (`product_status`),
  KEY `idx_is_hot` (`is_hot`),
  KEY `idx_is_new` (`is_new`),
  KEY `idx_is_recommend` (`is_recommend`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 COMMENT='商品信息表';

-- ----------------------------
-- 3、购物车表
-- ----------------------------
DROP TABLE IF EXISTS `mall_cart`;
CREATE TABLE `mall_cart` (
  `cart_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '购物车ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `spec_id` bigint(20) DEFAULT NULL COMMENT '商品规格ID',
  `quantity` int(11) NOT NULL DEFAULT 1 COMMENT '商品数量',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`cart_id`),
  UNIQUE KEY `uk_user_product_spec` (`user_id`,`product_id`,`spec_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_spec_id` (`spec_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 COMMENT='购物车表';

-- ----------------------------
-- 4、订单表
-- ----------------------------
DROP TABLE IF EXISTS `mall_order`;
CREATE TABLE `mall_order` (
  `order_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(32) NOT NULL COMMENT '订单号',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `address_id` bigint(20) DEFAULT NULL COMMENT '收货地址ID',
  `total_amount` decimal(10,2) NOT NULL COMMENT '订单总金额',
  `pay_amount` decimal(10,2) NOT NULL COMMENT '实付金额',
  `order_status` char(1) DEFAULT '0' COMMENT '订单状态（0待付款 1已付款 2已发货 3已完成 4已取消 5已退款）',
  `pay_status` char(1) DEFAULT '0' COMMENT '支付状态（0未支付 1已支付 2支付失败）',
  `pay_type` char(1) DEFAULT '1' COMMENT '支付方式（1支付宝 2微信 3余额）',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `delivery_time` datetime DEFAULT NULL COMMENT '发货时间',
  `finish_time` datetime DEFAULT NULL COMMENT '完成时间',
  `cancel_time` datetime DEFAULT NULL COMMENT '取消时间',
  `receiver_name` varchar(50) DEFAULT '' COMMENT '收货人姓名',
  `receiver_phone` varchar(20) DEFAULT '' COMMENT '收货人电话',
  `receiver_address` varchar(200) DEFAULT '' COMMENT '收货地址',
  `delivery_fee` decimal(10,2) DEFAULT 0.00 COMMENT '配送费',
  `remark` varchar(500) DEFAULT NULL COMMENT '订单备注',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`order_id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_address_id` (`address_id`),
  KEY `idx_order_status` (`order_status`),
  KEY `idx_pay_status` (`pay_status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 COMMENT='订单表';

-- ----------------------------
-- 5、订单详情表
-- ----------------------------
DROP TABLE IF EXISTS `mall_order_item`;
CREATE TABLE `mall_order_item` (
  `item_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '订单项ID',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `spec_id` bigint(20) DEFAULT NULL COMMENT '商品规格ID',
  `product_name` varchar(100) NOT NULL COMMENT '商品名称',
  `spec_name` varchar(100) DEFAULT '' COMMENT '规格名称',
  `product_image` varchar(200) DEFAULT '' COMMENT '商品图片',
  `spec_image` varchar(200) DEFAULT '' COMMENT '规格图片',
  `product_price` decimal(10,2) NOT NULL COMMENT '商品价格',
  `quantity` int(11) NOT NULL COMMENT '购买数量',
  `total_price` decimal(10,2) NOT NULL COMMENT '小计金额',
  PRIMARY KEY (`item_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_spec_id` (`spec_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 COMMENT='订单详情表';

-- ----------------------------
-- 6、支付记录表
-- ----------------------------
DROP TABLE IF EXISTS `mall_payment`;
CREATE TABLE `mall_payment` (
  `payment_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '支付记录ID',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `order_no` varchar(32) NOT NULL COMMENT '订单号',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `pay_amount` decimal(10,2) NOT NULL COMMENT '支付金额',
  `pay_type` char(1) NOT NULL COMMENT '支付方式（1支付宝 2微信 3余额）',
  `pay_status` char(1) DEFAULT '0' COMMENT '支付状态（0待支付 1支付成功 2支付失败 3已退款）',
  `trade_no` varchar(64) DEFAULT '' COMMENT '第三方交易号',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `notify_time` datetime DEFAULT NULL COMMENT '通知时间',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`payment_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_trade_no` (`trade_no`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 COMMENT='支付记录表';

-- ----------------------------
-- 初始化商品分类数据
-- ----------------------------
INSERT INTO `mall_category` VALUES (1, 0, '数码电器', '', 1, '0', '0', 'admin', NOW(), '', NULL, '数码电器分类');
INSERT INTO `mall_category` VALUES (2, 1, '手机通讯', '', 1, '0', '0', 'admin', NOW(), '', NULL, '手机通讯分类');
INSERT INTO `mall_category` VALUES (3, 1, '电脑办公', '', 2, '0', '0', 'admin', NOW(), '', NULL, '电脑办公分类');
INSERT INTO `mall_category` VALUES (4, 0, '服装鞋帽', '', 2, '0', '0', 'admin', NOW(), '', NULL, '服装鞋帽分类');
INSERT INTO `mall_category` VALUES (5, 4, '男装', '', 1, '0', '0', 'admin', NOW(), '', NULL, '男装分类');
INSERT INTO `mall_category` VALUES (6, 4, '女装', '', 2, '0', '0', 'admin', NOW(), '', NULL, '女装分类');
INSERT INTO `mall_category` VALUES (7, 0, '家居生活', '', 3, '0', '0', 'admin', NOW(), '', NULL, '家居生活分类');
INSERT INTO `mall_category` VALUES (8, 7, '家具', '', 1, '0', '0', 'admin', NOW(), '', NULL, '家具分类');
INSERT INTO `mall_category` VALUES (9, 7, '家电', '', 2, '0', '0', 'admin', NOW(), '', NULL, '家电分类');

-- ----------------------------
-- 商城管理菜单权限
-- ----------------------------
-- 商城管理主菜单
INSERT INTO sys_menu VALUES('3000', '商城管理', '0', '6', 'mall', NULL, '', '', 1, 0, 'M', '0', '0', '', 'shopping', 'admin', NOW(), '', NULL, '商城管理目录');

-- 商品分类管理菜单
INSERT INTO sys_menu VALUES('3001', '商品分类', '3000', '1', 'category', 'mall/category/index', '', '', 1, 0, 'C', '0', '0', 'mall:category:list', 'tree', 'admin', NOW(), '', NULL, '商品分类管理菜单');
INSERT INTO sys_menu VALUES('3002', '分类查询', '3001', '1', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:category:query', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3003', '分类新增', '3001', '2', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:category:add', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3004', '分类修改', '3001', '3', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:category:edit', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3005', '分类删除', '3001', '4', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:category:remove', '#', 'admin', NOW(), '', NULL, '');

-- 商品管理菜单
INSERT INTO sys_menu VALUES('3010', '商品管理', '3000', '2', 'product', 'mall/product/index', '', '', 1, 0, 'C', '0', '0', 'mall:product:list', 'goods', 'admin', NOW(), '', NULL, '商品管理菜单');
INSERT INTO sys_menu VALUES('3011', '商品查询', '3010', '1', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:product:query', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3012', '商品新增', '3010', '2', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:product:add', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3013', '商品修改', '3010', '3', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:product:edit', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3014', '商品删除', '3010', '4', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:product:remove', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3015', '商品导出', '3010', '5', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:product:export', '#', 'admin', NOW(), '', NULL, '');

-- 订单管理菜单
INSERT INTO sys_menu VALUES('3020', '订单管理', '3000', '3', 'order', 'mall/order/index', '', '', 1, 0, 'C', '0', '0', 'mall:order:list', 'list', 'admin', NOW(), '', NULL, '订单管理菜单');
INSERT INTO sys_menu VALUES('3021', '订单查询', '3020', '1', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:order:query', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3022', '订单详情', '3020', '2', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:order:detail', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3023', '订单发货', '3020', '3', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:order:delivery', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3024', '订单取消', '3020', '4', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:order:cancel', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3025', '订单导出', '3020', '5', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:order:export', '#', 'admin', NOW(), '', NULL, '');

-- 给管理员角色分配商城菜单权限
INSERT INTO sys_role_menu VALUES ('1', '3000');
INSERT INTO sys_role_menu VALUES ('1', '3001');
INSERT INTO sys_role_menu VALUES ('1', '3002');
INSERT INTO sys_role_menu VALUES ('1', '3003');
INSERT INTO sys_role_menu VALUES ('1', '3004');
INSERT INTO sys_role_menu VALUES ('1', '3005');
INSERT INTO sys_role_menu VALUES ('1', '3010');
INSERT INTO sys_role_menu VALUES ('1', '3011');
INSERT INTO sys_role_menu VALUES ('1', '3012');
INSERT INTO sys_role_menu VALUES ('1', '3013');
INSERT INTO sys_role_menu VALUES ('1', '3014');
INSERT INTO sys_role_menu VALUES ('1', '3015');
INSERT INTO sys_role_menu VALUES ('1', '3020');
INSERT INTO sys_role_menu VALUES ('1', '3021');
INSERT INTO sys_role_menu VALUES ('1', '3022');
INSERT INTO sys_role_menu VALUES ('1', '3023');
INSERT INTO sys_role_menu VALUES ('1', '3024');
INSERT INTO sys_role_menu VALUES ('1', '3025');

-- 商品规格管理菜单
INSERT INTO sys_menu VALUES('3030', '商品规格', '3000', '4', 'spec', 'mall/spec/index', '', '', 1, 0, 'C', '0', '0', 'mall:spec:list', 'component', 'admin', NOW(), '', NULL, '商品规格管理菜单');
INSERT INTO sys_menu VALUES('3031', '规格查询', '3030', '1', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:spec:query', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3032', '规格新增', '3030', '2', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:spec:add', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3033', '规格修改', '3030', '3', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:spec:edit', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3034', '规格删除', '3030', '4', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:spec:remove', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3035', '规格导出', '3030', '5', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:spec:export', '#', 'admin', NOW(), '', NULL, '');

-- 物流管理菜单
INSERT INTO sys_menu VALUES('3040', '物流管理', '3000', '5', 'logistics', 'mall/logistics/index', '', '', 1, 0, 'C', '0', '0', 'mall:logistics:list', 'guide', 'admin', NOW(), '', NULL, '物流管理菜单');
INSERT INTO sys_menu VALUES('3041', '物流查询', '3040', '1', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:logistics:query', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3042', '物流新增', '3040', '2', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:logistics:add', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3043', '物流修改', '3040', '3', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:logistics:edit', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3044', '物流删除', '3040', '4', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:logistics:remove', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3045', '物流导出', '3040', '5', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:logistics:export', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3046', '创建发货', '3040', '6', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:logistics:delivery', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3047', '状态更新', '3040', '7', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:logistics:status', '#', 'admin', NOW(), '', NULL, '');

-- 给管理员角色分配新菜单权限
INSERT INTO sys_role_menu VALUES ('1', '3030');
INSERT INTO sys_role_menu VALUES ('1', '3031');
INSERT INTO sys_role_menu VALUES ('1', '3032');
INSERT INTO sys_role_menu VALUES ('1', '3033');
INSERT INTO sys_role_menu VALUES ('1', '3034');
INSERT INTO sys_role_menu VALUES ('1', '3035');
INSERT INTO sys_role_menu VALUES ('1', '3040');
INSERT INTO sys_role_menu VALUES ('1', '3041');
INSERT INTO sys_role_menu VALUES ('1', '3042');
INSERT INTO sys_role_menu VALUES ('1', '3043');
INSERT INTO sys_role_menu VALUES ('1', '3044');
INSERT INTO sys_role_menu VALUES ('1', '3045');
INSERT INTO sys_role_menu VALUES ('1', '3046');
INSERT INTO sys_role_menu VALUES ('1', '3047');

-- ----------------------------
-- 7、商品规格表
-- ----------------------------
DROP TABLE IF EXISTS `mall_product_spec`;
CREATE TABLE `mall_product_spec` (
  `spec_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '规格ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `spec_name` varchar(100) NOT NULL COMMENT '规格名称',
  `spec_image` varchar(500) DEFAULT '' COMMENT '规格图片',
  `sale_price` decimal(10,2) NOT NULL COMMENT '售卖价格',
  `supply_price` decimal(10,2) DEFAULT 0.00 COMMENT '供货价格',
  `stock_quantity` int(11) DEFAULT 0 COMMENT '库存数量',
  `spec_status` char(1) DEFAULT '0' COMMENT '规格状态（0上架 1下架）',
  `sort_order` int(4) DEFAULT 0 COMMENT '显示顺序',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`spec_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_spec_status` (`spec_status`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 COMMENT='商品规格表';

-- ----------------------------
-- 8、订单物流表
-- ----------------------------
DROP TABLE IF EXISTS `mall_order_logistics`;
CREATE TABLE `mall_order_logistics` (
  `logistics_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '物流ID',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `order_no` varchar(32) NOT NULL COMMENT '订单号',
  `logistics_type` char(1) DEFAULT '1' COMMENT '物流类型（1发货 2退货）',
  `logistics_company` varchar(50) DEFAULT '' COMMENT '物流公司',
  `logistics_no` varchar(50) DEFAULT '' COMMENT '物流单号',
  `logistics_status` char(1) DEFAULT '0' COMMENT '物流状态（0待发货 1已发货 2运输中 3已签收 4异常）',
  `sender_name` varchar(50) DEFAULT '' COMMENT '发件人姓名',
  `sender_phone` varchar(20) DEFAULT '' COMMENT '发件人电话',
  `sender_address` varchar(200) DEFAULT '' COMMENT '发件地址',
  `receiver_name` varchar(50) DEFAULT '' COMMENT '收件人姓名',
  `receiver_phone` varchar(20) DEFAULT '' COMMENT '收件人电话',
  `receiver_address` varchar(200) DEFAULT '' COMMENT '收件地址',
  `send_time` datetime DEFAULT NULL COMMENT '发货时间',
  `receive_time` datetime DEFAULT NULL COMMENT '签收时间',
  `logistics_info` text COMMENT '物流跟踪信息（JSON格式）',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`logistics_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_logistics_no` (`logistics_no`),
  KEY `idx_logistics_status` (`logistics_status`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 COMMENT='订单物流表';
