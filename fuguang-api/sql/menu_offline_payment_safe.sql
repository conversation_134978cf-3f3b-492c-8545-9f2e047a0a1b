-- 线下支付管理菜单配置（使用安全的菜单ID）
-- 使用3000+的菜单ID避免冲突

-- 查询当前最大菜单ID
SELECT MAX(menu_id) as max_menu_id FROM sys_menu;

-- 线下支付管理目录菜单
INSERT INTO sys_menu VALUES('3001', '线下支付管理', '2000', '4', 'offlinePayment', '', '', '', 1, 0, 'M', '0', '0', '', 'money', 'admin', sysdate(), '', null, '线下支付管理目录');

-- 线下支付订单管理菜单
INSERT INTO sys_menu VALUES('3002', '支付订单管理', '3001', '1', 'offlinePayment', 'fuguang/offlinePayment/index', '', '', 1, 0, 'C', '0', '0', 'fuguang:offlinePayment:list', 'shopping', 'admin', sysdate(), '', null, '线下支付订单管理菜单');
INSERT INTO sys_menu VALUES('3003', '订单查询', '3002', '1', '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:offlinePayment:query', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES('3004', '订单新增', '3002', '2', '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:offlinePayment:add', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES('3005', '订单修改', '3002', '3', '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:offlinePayment:edit', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES('3006', '订单删除', '3002', '4', '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:offlinePayment:remove', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES('3007', '订单导出', '3002', '5', '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:offlinePayment:export', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES('3008', '重新转账', '3002', '6', '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:offlinePayment:transfer', '#', 'admin', sysdate(), '', null, '');

-- 商家二维码管理菜单
INSERT INTO sys_menu VALUES('3009', '二维码管理', '3001', '2', 'merchantQrcode', 'fuguang/merchantQrcode/index', '', '', 1, 0, 'C', '0', '0', 'fuguang:merchantQrcode:list', 'qrcode', 'admin', sysdate(), '', null, '商家二维码管理菜单');
INSERT INTO sys_menu VALUES('3010', '二维码查询', '3009', '1', '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:merchantQrcode:query', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES('3011', '二维码新增', '3009', '2', '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:merchantQrcode:add', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES('3012', '二维码修改', '3009', '3', '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:merchantQrcode:edit', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES('3013', '二维码删除', '3009', '4', '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:merchantQrcode:remove', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES('3014', '二维码导出', '3009', '5', '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:merchantQrcode:export', '#', 'admin', sysdate(), '', null, '');

-- 线下支付统计页面
INSERT INTO sys_menu VALUES('3015', '支付统计', '3001', '3', 'statistics', 'fuguang/offlinePayment/statistics', '', '', 1, 0, 'C', '0', '0', 'fuguang:offlinePayment:list', 'chart', 'admin', sysdate(), '', null, '线下支付统计页面');

-- 线下支付相关数据字典（检查是否已存在）
INSERT INTO sys_dict_type VALUES(200, '支付状态', 'pay_status', '0', 'admin', sysdate(), '', null, '支付状态列表')
ON DUPLICATE KEY UPDATE dict_name = VALUES(dict_name);

INSERT INTO sys_dict_type VALUES(201, '支付方式', 'pay_type', '0', 'admin', sysdate(), '', null, '支付方式列表')
ON DUPLICATE KEY UPDATE dict_name = VALUES(dict_name);

INSERT INTO sys_dict_type VALUES(202, '转账状态', 'transfer_status', '0', 'admin', sysdate(), '', null, '转账状态列表')
ON DUPLICATE KEY UPDATE dict_name = VALUES(dict_name);

-- 支付状态字典数据
INSERT INTO sys_dict_data VALUES(200, 1, '待支付', '0', 'pay_status', '', 'warning', 'N', '0', 'admin', sysdate(), '', null, '待支付状态')
ON DUPLICATE KEY UPDATE dict_label = VALUES(dict_label);

INSERT INTO sys_dict_data VALUES(201, 2, '支付成功', '1', 'pay_status', '', 'success', 'N', '0', 'admin', sysdate(), '', null, '支付成功状态')
ON DUPLICATE KEY UPDATE dict_label = VALUES(dict_label);

INSERT INTO sys_dict_data VALUES(202, 3, '支付失败', '2', 'pay_status', '', 'danger', 'N', '0', 'admin', sysdate(), '', null, '支付失败状态')
ON DUPLICATE KEY UPDATE dict_label = VALUES(dict_label);

INSERT INTO sys_dict_data VALUES(203, 4, '已退款', '3', 'pay_status', '', 'info', 'N', '0', 'admin', sysdate(), '', null, '已退款状态')
ON DUPLICATE KEY UPDATE dict_label = VALUES(dict_label);

-- 支付方式字典数据
INSERT INTO sys_dict_data VALUES(204, 1, '支付宝', '1', 'pay_type', '', 'primary', 'N', '0', 'admin', sysdate(), '', null, '支付宝支付')
ON DUPLICATE KEY UPDATE dict_label = VALUES(dict_label);

INSERT INTO sys_dict_data VALUES(205, 2, '微信', '2', 'pay_type', '', 'success', 'N', '0', 'admin', sysdate(), '', null, '微信支付')
ON DUPLICATE KEY UPDATE dict_label = VALUES(dict_label);

INSERT INTO sys_dict_data VALUES(206, 3, '余额', '3', 'pay_type', '', 'warning', 'N', '0', 'admin', sysdate(), '', null, '余额支付')
ON DUPLICATE KEY UPDATE dict_label = VALUES(dict_label);

-- 转账状态字典数据
INSERT INTO sys_dict_data VALUES(207, 1, '未转账', '0', 'transfer_status', '', 'info', 'N', '0', 'admin', sysdate(), '', null, '未转账状态')
ON DUPLICATE KEY UPDATE dict_label = VALUES(dict_label);

INSERT INTO sys_dict_data VALUES(208, 2, '转账中', '1', 'transfer_status', '', 'warning', 'N', '0', 'admin', sysdate(), '', null, '转账中状态')
ON DUPLICATE KEY UPDATE dict_label = VALUES(dict_label);

INSERT INTO sys_dict_data VALUES(209, 3, '转账成功', '2', 'transfer_status', '', 'success', 'N', '0', 'admin', sysdate(), '', null, '转账成功状态')
ON DUPLICATE KEY UPDATE dict_label = VALUES(dict_label);

INSERT INTO sys_dict_data VALUES(210, 4, '转账失败', '3', 'transfer_status', '', 'danger', 'N', '0', 'admin', sysdate(), '', null, '转账失败状态')
ON DUPLICATE KEY UPDATE dict_label = VALUES(dict_label);

-- 为admin用户角色分配菜单权限
INSERT INTO sys_role_menu SELECT '1', menu_id FROM sys_menu WHERE menu_id BETWEEN 3001 AND 3015;

-- 验证菜单是否插入成功
SELECT menu_id, menu_name, parent_id, path, component, perms FROM sys_menu WHERE menu_id BETWEEN 3001 AND 3015 ORDER BY menu_id;

-- 验证数据字典是否插入成功
SELECT dict_id, dict_name, dict_type FROM sys_dict_type WHERE dict_type IN ('pay_status', 'pay_type', 'transfer_status');

-- 验证角色菜单关联是否成功
SELECT rm.role_id, m.menu_name FROM sys_role_menu rm 
JOIN sys_menu m ON rm.menu_id = m.menu_id 
WHERE rm.menu_id BETWEEN 3001 AND 3015;
