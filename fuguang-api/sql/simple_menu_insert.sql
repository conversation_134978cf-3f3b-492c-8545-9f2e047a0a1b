-- 简单的线下支付菜单插入脚本
-- 直接执行即可，使用固定ID避免冲突

-- 线下支付管理目录 (ID: 4001)
INSERT INTO sys_menu VALUES(4001, '线下支付管理', 2000, 4, 'offlinePayment', '', '', '', 1, 0, 'M', '0', '0', '', 'money', 'admin', NOW(), '', null, '线下支付管理目录');

-- 支付订单管理 (ID: 4002)
INSERT INTO sys_menu VALUES(4002, '支付订单管理', 4001, 1, 'offlinePayment', 'fuguang/offlinePayment/index', '', '', 1, 0, 'C', '0', '0', 'fuguang:offlinePayment:list', 'shopping', 'admin', NOW(), '', null, '线下支付订单管理菜单');
INSERT INTO sys_menu VALUES(4003, '订单查询', 4002, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:offlinePayment:query', '#', 'admin', NOW(), '', null, '');
INSERT INTO sys_menu VALUES(4004, '订单新增', 4002, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:offlinePayment:add', '#', 'admin', NOW(), '', null, '');
INSERT INTO sys_menu VALUES(4005, '订单修改', 4002, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:offlinePayment:edit', '#', 'admin', NOW(), '', null, '');
INSERT INTO sys_menu VALUES(4006, '订单删除', 4002, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:offlinePayment:remove', '#', 'admin', NOW(), '', null, '');
INSERT INTO sys_menu VALUES(4007, '订单导出', 4002, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:offlinePayment:export', '#', 'admin', NOW(), '', null, '');
INSERT INTO sys_menu VALUES(4008, '重新转账', 4002, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:offlinePayment:transfer', '#', 'admin', NOW(), '', null, '');

-- 商家二维码管理 (ID: 4009)
INSERT INTO sys_menu VALUES(4009, '二维码管理', 4001, 2, 'merchantQrcode', 'fuguang/merchantQrcode/index', '', '', 1, 0, 'C', '0', '0', 'fuguang:merchantQrcode:list', 'qrcode', 'admin', NOW(), '', null, '商家二维码管理菜单');
INSERT INTO sys_menu VALUES(4010, '二维码查询', 4009, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:merchantQrcode:query', '#', 'admin', NOW(), '', null, '');
INSERT INTO sys_menu VALUES(4011, '二维码新增', 4009, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:merchantQrcode:add', '#', 'admin', NOW(), '', null, '');
INSERT INTO sys_menu VALUES(4012, '二维码修改', 4009, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:merchantQrcode:edit', '#', 'admin', NOW(), '', null, '');
INSERT INTO sys_menu VALUES(4013, '二维码删除', 4009, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:merchantQrcode:remove', '#', 'admin', NOW(), '', null, '');
INSERT INTO sys_menu VALUES(4014, '二维码导出', 4009, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:merchantQrcode:export', '#', 'admin', NOW(), '', null, '');

-- 支付统计 (ID: 4015)
INSERT INTO sys_menu VALUES(4015, '支付统计', 4001, 3, 'statistics', 'fuguang/offlinePayment/statistics', '', '', 1, 0, 'C', '0', '0', 'fuguang:offlinePayment:list', 'chart', 'admin', NOW(), '', null, '线下支付统计页面');

-- 为admin角色分配菜单权限
INSERT INTO sys_role_menu VALUES(1, 4001);
INSERT INTO sys_role_menu VALUES(1, 4002);
INSERT INTO sys_role_menu VALUES(1, 4003);
INSERT INTO sys_role_menu VALUES(1, 4004);
INSERT INTO sys_role_menu VALUES(1, 4005);
INSERT INTO sys_role_menu VALUES(1, 4006);
INSERT INTO sys_role_menu VALUES(1, 4007);
INSERT INTO sys_role_menu VALUES(1, 4008);
INSERT INTO sys_role_menu VALUES(1, 4009);
INSERT INTO sys_role_menu VALUES(1, 4010);
INSERT INTO sys_role_menu VALUES(1, 4011);
INSERT INTO sys_role_menu VALUES(1, 4012);
INSERT INTO sys_role_menu VALUES(1, 4013);
INSERT INTO sys_role_menu VALUES(1, 4014);
INSERT INTO sys_role_menu VALUES(1, 4015);

-- 插入数据字典
INSERT INTO sys_dict_type VALUES(NULL, '支付状态', 'pay_status', '0', 'admin', NOW(), '', null, '支付状态列表');
INSERT INTO sys_dict_type VALUES(NULL, '支付方式', 'pay_type', '0', 'admin', NOW(), '', null, '支付方式列表');
INSERT INTO sys_dict_type VALUES(NULL, '转账状态', 'transfer_status', '0', 'admin', NOW(), '', null, '转账状态列表');

INSERT INTO sys_dict_data VALUES(NULL, 1, '待支付', '0', 'pay_status', '', 'warning', 'N', '0', 'admin', NOW(), '', null, '待支付状态');
INSERT INTO sys_dict_data VALUES(NULL, 2, '支付成功', '1', 'pay_status', '', 'success', 'N', '0', 'admin', NOW(), '', null, '支付成功状态');
INSERT INTO sys_dict_data VALUES(NULL, 3, '支付失败', '2', 'pay_status', '', 'danger', 'N', '0', 'admin', NOW(), '', null, '支付失败状态');
INSERT INTO sys_dict_data VALUES(NULL, 4, '已退款', '3', 'pay_status', '', 'info', 'N', '0', 'admin', NOW(), '', null, '已退款状态');

INSERT INTO sys_dict_data VALUES(NULL, 1, '支付宝', '1', 'pay_type', '', 'primary', 'N', '0', 'admin', NOW(), '', null, '支付宝支付');
INSERT INTO sys_dict_data VALUES(NULL, 2, '微信', '2', 'pay_type', '', 'success', 'N', '0', 'admin', NOW(), '', null, '微信支付');
INSERT INTO sys_dict_data VALUES(NULL, 3, '余额', '3', 'pay_type', '', 'warning', 'N', '0', 'admin', NOW(), '', null, '余额支付');

INSERT INTO sys_dict_data VALUES(NULL, 1, '未转账', '0', 'transfer_status', '', 'info', 'N', '0', 'admin', NOW(), '', null, '未转账状态');
INSERT INTO sys_dict_data VALUES(NULL, 2, '转账中', '1', 'transfer_status', '', 'warning', 'N', '0', 'admin', NOW(), '', null, '转账中状态');
INSERT INTO sys_dict_data VALUES(NULL, 3, '转账成功', '2', 'transfer_status', '', 'success', 'N', '0', 'admin', NOW(), '', null, '转账成功状态');
INSERT INTO sys_dict_data VALUES(NULL, 4, '转账失败', '3', 'transfer_status', '', 'danger', 'N', '0', 'admin', NOW(), '', null, '转账失败状态');
