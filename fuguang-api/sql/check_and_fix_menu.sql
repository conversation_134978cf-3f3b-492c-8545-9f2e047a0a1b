-- 检查和修复线下支付管理菜单

-- 1. 检查当前菜单状态
SELECT '=== 当前浮光壁垒菜单结构 ===' as info;
SELECT menu_id, menu_name, parent_id, path, component, perms, visible 
FROM sys_menu 
WHERE parent_id = 2000 OR menu_id = 2000
ORDER BY menu_id;

-- 2. 检查是否存在线下支付相关菜单
SELECT '=== 检查线下支付菜单是否存在 ===' as info;
SELECT menu_id, menu_name, parent_id, path, component 
FROM sys_menu 
WHERE menu_name LIKE '%线下支付%' OR menu_name LIKE '%二维码%' OR menu_name LIKE '%支付订单%'
ORDER BY menu_id;

-- 3. 检查数据字典是否存在
SELECT '=== 检查数据字典是否存在 ===' as info;
SELECT dict_id, dict_name, dict_type, status 
FROM sys_dict_type 
WHERE dict_type IN ('pay_status', 'pay_type', 'transfer_status');

-- 4. 如果菜单不存在，则插入菜单（使用动态ID）
-- 先获取当前最大菜单ID
SET @max_menu_id = (SELECT COALESCE(MAX(menu_id), 2000) FROM sys_menu WHERE menu_id < 9000);

-- 插入线下支付管理目录
SET @menu_id_base = @max_menu_id + 1;
INSERT IGNORE INTO sys_menu VALUES(@menu_id_base, '线下支付管理', '2000', '4', 'offlinePayment', '', '', '', 1, 0, 'M', '0', '0', '', 'money', 'admin', NOW(), '', null, '线下支付管理目录');

-- 插入支付订单管理菜单
SET @menu_id_payment = @menu_id_base + 1;
INSERT IGNORE INTO sys_menu VALUES(@menu_id_payment, '支付订单管理', @menu_id_base, '1', 'offlinePayment', 'fuguang/offlinePayment/index', '', '', 1, 0, 'C', '0', '0', 'fuguang:offlinePayment:list', 'shopping', 'admin', NOW(), '', null, '线下支付订单管理菜单');

-- 插入支付订单功能权限
INSERT IGNORE INTO sys_menu VALUES(@menu_id_payment + 1, '订单查询', @menu_id_payment, '1', '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:offlinePayment:query', '#', 'admin', NOW(), '', null, '');
INSERT IGNORE INTO sys_menu VALUES(@menu_id_payment + 2, '订单新增', @menu_id_payment, '2', '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:offlinePayment:add', '#', 'admin', NOW(), '', null, '');
INSERT IGNORE INTO sys_menu VALUES(@menu_id_payment + 3, '订单修改', @menu_id_payment, '3', '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:offlinePayment:edit', '#', 'admin', NOW(), '', null, '');
INSERT IGNORE INTO sys_menu VALUES(@menu_id_payment + 4, '订单删除', @menu_id_payment, '4', '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:offlinePayment:remove', '#', 'admin', NOW(), '', null, '');
INSERT IGNORE INTO sys_menu VALUES(@menu_id_payment + 5, '订单导出', @menu_id_payment, '5', '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:offlinePayment:export', '#', 'admin', NOW(), '', null, '');
INSERT IGNORE INTO sys_menu VALUES(@menu_id_payment + 6, '重新转账', @menu_id_payment, '6', '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:offlinePayment:transfer', '#', 'admin', NOW(), '', null, '');

-- 插入商家二维码管理菜单
SET @menu_id_qrcode = @menu_id_payment + 7;
INSERT IGNORE INTO sys_menu VALUES(@menu_id_qrcode, '二维码管理', @menu_id_base, '2', 'merchantQrcode', 'fuguang/merchantQrcode/index', '', '', 1, 0, 'C', '0', '0', 'fuguang:merchantQrcode:list', 'qrcode', 'admin', NOW(), '', null, '商家二维码管理菜单');

-- 插入二维码功能权限
INSERT IGNORE INTO sys_menu VALUES(@menu_id_qrcode + 1, '二维码查询', @menu_id_qrcode, '1', '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:merchantQrcode:query', '#', 'admin', NOW(), '', null, '');
INSERT IGNORE INTO sys_menu VALUES(@menu_id_qrcode + 2, '二维码新增', @menu_id_qrcode, '2', '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:merchantQrcode:add', '#', 'admin', NOW(), '', null, '');
INSERT IGNORE INTO sys_menu VALUES(@menu_id_qrcode + 3, '二维码修改', @menu_id_qrcode, '3', '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:merchantQrcode:edit', '#', 'admin', NOW(), '', null, '');
INSERT IGNORE INTO sys_menu VALUES(@menu_id_qrcode + 4, '二维码删除', @menu_id_qrcode, '4', '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:merchantQrcode:remove', '#', 'admin', NOW(), '', null, '');
INSERT IGNORE INTO sys_menu VALUES(@menu_id_qrcode + 5, '二维码导出', @menu_id_qrcode, '5', '', '', '', '', 1, 0, 'F', '0', '0', 'fuguang:merchantQrcode:export', '#', 'admin', NOW(), '', null, '');

-- 插入支付统计页面
SET @menu_id_stats = @menu_id_qrcode + 6;
INSERT IGNORE INTO sys_menu VALUES(@menu_id_stats, '支付统计', @menu_id_base, '3', 'statistics', 'fuguang/offlinePayment/statistics', '', '', 1, 0, 'C', '0', '0', 'fuguang:offlinePayment:list', 'chart', 'admin', NOW(), '', null, '线下支付统计页面');

-- 5. 插入数据字典（如果不存在）
INSERT IGNORE INTO sys_dict_type VALUES(NULL, '支付状态', 'pay_status', '0', 'admin', NOW(), '', null, '支付状态列表');
INSERT IGNORE INTO sys_dict_type VALUES(NULL, '支付方式', 'pay_type', '0', 'admin', NOW(), '', null, '支付方式列表');
INSERT IGNORE INTO sys_dict_type VALUES(NULL, '转账状态', 'transfer_status', '0', 'admin', NOW(), '', null, '转账状态列表');

-- 插入字典数据
INSERT IGNORE INTO sys_dict_data VALUES(NULL, 1, '待支付', '0', 'pay_status', '', 'warning', 'N', '0', 'admin', NOW(), '', null, '待支付状态');
INSERT IGNORE INTO sys_dict_data VALUES(NULL, 2, '支付成功', '1', 'pay_status', '', 'success', 'N', '0', 'admin', NOW(), '', null, '支付成功状态');
INSERT IGNORE INTO sys_dict_data VALUES(NULL, 3, '支付失败', '2', 'pay_status', '', 'danger', 'N', '0', 'admin', NOW(), '', null, '支付失败状态');
INSERT IGNORE INTO sys_dict_data VALUES(NULL, 4, '已退款', '3', 'pay_status', '', 'info', 'N', '0', 'admin', NOW(), '', null, '已退款状态');

INSERT IGNORE INTO sys_dict_data VALUES(NULL, 1, '支付宝', '1', 'pay_type', '', 'primary', 'N', '0', 'admin', NOW(), '', null, '支付宝支付');
INSERT IGNORE INTO sys_dict_data VALUES(NULL, 2, '微信', '2', 'pay_type', '', 'success', 'N', '0', 'admin', NOW(), '', null, '微信支付');
INSERT IGNORE INTO sys_dict_data VALUES(NULL, 3, '余额', '3', 'pay_type', '', 'warning', 'N', '0', 'admin', NOW(), '', null, '余额支付');

INSERT IGNORE INTO sys_dict_data VALUES(NULL, 1, '未转账', '0', 'transfer_status', '', 'info', 'N', '0', 'admin', NOW(), '', null, '未转账状态');
INSERT IGNORE INTO sys_dict_data VALUES(NULL, 2, '转账中', '1', 'transfer_status', '', 'warning', 'N', '0', 'admin', NOW(), '', null, '转账中状态');
INSERT IGNORE INTO sys_dict_data VALUES(NULL, 3, '转账成功', '2', 'transfer_status', '', 'success', 'N', '0', 'admin', NOW(), '', null, '转账成功状态');
INSERT IGNORE INTO sys_dict_data VALUES(NULL, 4, '转账失败', '3', 'transfer_status', '', 'danger', 'N', '0', 'admin', NOW(), '', null, '转账失败状态');

-- 6. 为admin角色分配新菜单权限
INSERT IGNORE INTO sys_role_menu 
SELECT '1', menu_id FROM sys_menu 
WHERE menu_name IN ('线下支付管理', '支付订单管理', '二维码管理', '支付统计')
   OR parent_id IN (
       SELECT menu_id FROM sys_menu 
       WHERE menu_name IN ('支付订单管理', '二维码管理')
   );

-- 7. 验证结果
SELECT '=== 插入后的线下支付菜单结构 ===' as info;
SELECT menu_id, menu_name, parent_id, path, component, perms 
FROM sys_menu 
WHERE menu_name LIKE '%线下支付%' OR menu_name LIKE '%二维码%' OR menu_name LIKE '%支付订单%' OR menu_name LIKE '%支付统计%'
   OR parent_id IN (
       SELECT m2.menu_id FROM sys_menu m2 
       WHERE m2.menu_name = '线下支付管理'
   )
ORDER BY menu_id;

SELECT '=== 数据字典验证 ===' as info;
SELECT dict_type, COUNT(*) as count FROM sys_dict_data 
WHERE dict_type IN ('pay_status', 'pay_type', 'transfer_status')
GROUP BY dict_type;

SELECT '=== 完成 ===' as info;
