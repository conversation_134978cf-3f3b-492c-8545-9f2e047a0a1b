-- 线下支付订单表
DROP TABLE IF EXISTS `offline_payment`;
CREATE TABLE `offline_payment` (
  `payment_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '支付记录ID',
  `merchant_id` bigint(20) NOT NULL COMMENT '商家ID（商家申请ID）',
  `merchant_name` varchar(100) NOT NULL COMMENT '商家名称',
  `order_no` varchar(64) NOT NULL COMMENT '支付订单号',
  `pay_amount` decimal(10,2) NOT NULL COMMENT '支付金额',
  `pay_type` char(1) NOT NULL DEFAULT '1' COMMENT '支付方式（1支付宝 2微信 3余额）',
  `pay_status` char(1) NOT NULL DEFAULT '0' COMMENT '支付状态（0待支付 1支付成功 2支付失败 3已退款）',
  `trade_no` varchar(64) DEFAULT NULL COMMENT '第三方交易号',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `notify_time` datetime DEFAULT NULL COMMENT '通知时间',
  `transfer_status` char(1) DEFAULT '0' COMMENT '转账状态（0未转账 1转账中 2转账成功 3转账失败）',
  `transfer_no` varchar(64) DEFAULT NULL COMMENT '转账单号',
  `transfer_time` datetime DEFAULT NULL COMMENT '转账时间',
  `transfer_amount` decimal(10,2) DEFAULT 0.00 COMMENT '转账金额',
  `platform_fee` decimal(10,2) DEFAULT 0.00 COMMENT '平台手续费',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`payment_id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_pay_status` (`pay_status`),
  KEY `idx_transfer_status` (`transfer_status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT='线下支付订单表';

-- 商家二维码信息表
DROP TABLE IF EXISTS `merchant_qrcode`;
CREATE TABLE `merchant_qrcode` (
  `qrcode_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '二维码ID',
  `merchant_id` bigint(20) NOT NULL COMMENT '商家ID（商家申请ID）',
  `merchant_name` varchar(100) NOT NULL COMMENT '商家名称',
  `qrcode_content` text NOT NULL COMMENT '二维码内容',
  `qrcode_url` varchar(500) DEFAULT NULL COMMENT '二维码图片URL',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`qrcode_id`),
  UNIQUE KEY `uk_merchant_id` (`merchant_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT='商家二维码信息表';
