-- 佣金账单系统数据库脚本
-- 执行前请确保已经执行了 task_payment.sql 中的基础表创建

-- 检查并创建用户余额表
DROP TABLE IF EXISTS `user_balance`;
CREATE TABLE `user_balance` (
  `balance_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '余额记录ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `user_name` varchar(50) NOT NULL COMMENT '用户昵称',
  `total_balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总余额',
  `available_balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '可用余额',
  `frozen_balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '冻结余额',
  `total_income` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '累计收入',
  `total_withdraw` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '累计提现',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`balance_id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户余额表';

-- 检查并创建余额变动记录表
DROP TABLE IF EXISTS `balance_record`;
CREATE TABLE `balance_record` (
  `record_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `user_name` varchar(50) NOT NULL COMMENT '用户昵称',
  `change_type` char(1) NOT NULL COMMENT '变动类型（1收入 2支出）',
  `income_type` char(1) DEFAULT NULL COMMENT '收入类型（1任务佣金 2推荐奖励 3其他收入）',
  `change_amount` decimal(10,2) NOT NULL COMMENT '变动金额',
  `balance_before` decimal(10,2) NOT NULL COMMENT '变动前余额',
  `balance_after` decimal(10,2) NOT NULL COMMENT '变动后余额',
  `business_type` varchar(20) NOT NULL COMMENT '业务类型（task_commission任务佣金 withdraw提现 refund退款等）',
  `business_id` bigint(20) DEFAULT NULL COMMENT '关联业务ID',
  `business_no` varchar(64) DEFAULT NULL COMMENT '关联业务单号',
  `description` varchar(200) NOT NULL COMMENT '变动说明',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`record_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_change_type` (`change_type`),
  KEY `idx_income_type` (`income_type`),
  KEY `idx_business_type` (`business_type`),
  KEY `idx_business_id` (`business_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='余额变动记录表';

-- 检查并创建佣金账单表
DROP TABLE IF EXISTS `commission_bill`;
CREATE TABLE `commission_bill` (
  `bill_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '账单ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `user_name` varchar(50) NOT NULL COMMENT '用户昵称',
  `bill_year` int(4) NOT NULL COMMENT '账单年份',
  `bill_month` int(2) NOT NULL COMMENT '账单月份',
  `total_income` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总收入',
  `task_commission` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '任务佣金收入',
  `recommend_reward` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '推荐奖励收入',
  `other_income` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '其他收入',
  `total_withdraw` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总提现',
  `withdraw_count` int(11) NOT NULL DEFAULT '0' COMMENT '提现次数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`bill_id`),
  UNIQUE KEY `uk_user_month` (`user_id`, `bill_year`, `bill_month`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_bill_date` (`bill_year`, `bill_month`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='佣金账单表';

-- 插入测试数据（可选）
-- 为现有用户初始化余额记录
INSERT IGNORE INTO `user_balance` (`user_id`, `user_name`, `total_balance`, `available_balance`, `frozen_balance`, `total_income`, `total_withdraw`)
SELECT 
    `user_id`, 
    `nick_name`, 
    0.00, 
    0.00, 
    0.00, 
    0.00, 
    0.00
FROM `app_user` 
WHERE `del_flag` = '0' AND `status` = '0';

-- 创建索引优化查询性能
CREATE INDEX `idx_balance_record_user_time` ON `balance_record` (`user_id`, `create_time`);
CREATE INDEX `idx_commission_bill_user_time` ON `commission_bill` (`user_id`, `bill_year`, `bill_month`);

-- 显示创建结果
SELECT '佣金账单系统表创建完成' as message;
SELECT 
    TABLE_NAME as '表名',
    TABLE_COMMENT as '表说明',
    TABLE_ROWS as '记录数'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN ('user_balance', 'balance_record', 'commission_bill')
ORDER BY TABLE_NAME;
