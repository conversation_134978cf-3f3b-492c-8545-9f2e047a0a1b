-- 线下支付功能测试数据

-- 1. 更新现有商家申请，添加支付宝账号信息
UPDATE merchant_application 
SET alipay_account = '<EMAIL>', 
    alipay_name = '测试商家' 
WHERE application_id = 1 AND application_status = '1';

-- 2. 插入测试商家申请（如果不存在）
INSERT INTO merchant_application (
    user_id, business_license, shop_name, shop_address, 
    legal_person, legal_id_card, legal_phone, business_scope,
    alipay_account, alipay_name, application_status,
    create_time, create_by
) VALUES (
    1, '/upload/test_license.jpg', '测试商家店铺', '测试地址123号',
    '张三', '123456789012345678', '***********', '餐饮服务',
    '<EMAIL>', '张三', '1',
    NOW(), 'admin'
) ON DUPLICATE KEY UPDATE 
    alipay_account = VALUES(alipay_account),
    alipay_name = VALUES(alipay_name);

-- 3. 查询测试数据
SELECT 
    application_id,
    shop_name,
    alipay_account,
    alipay_name,
    application_status
FROM merchant_application 
WHERE application_status = '1'
LIMIT 5;

-- 4. 测试线下支付订单查询
SELECT 
    payment_id,
    merchant_name,
    order_no,
    pay_amount,
    pay_status,
    transfer_status,
    create_time
FROM offline_payment 
ORDER BY create_time DESC 
LIMIT 10;

-- 5. 测试商家二维码查询
SELECT 
    qrcode_id,
    merchant_name,
    qrcode_content,
    status,
    create_time
FROM merchant_qrcode 
ORDER BY create_time DESC 
LIMIT 10;
