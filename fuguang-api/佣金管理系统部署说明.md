# 佣金管理系统部署说明

## 概述

本文档描述了浮光壁垒佣金管理系统的完整部署流程，包括后端API、前端页面和数据库配置。

## 系统功能

### 核心功能
1. **用户余额管理** - 管理用户余额、冻结/解冻、手动调整
2. **佣金账单管理** - 按月统计用户收入支出情况
3. **提现管理** - 提现申请审核、批量处理、状态管理
4. **余额变动记录** - 详细记录每笔余额变动
5. **数据统计分析** - 平台收支概览、趋势分析、分布统计

### 自动化功能
- 任务完成时自动分配佣金给接收者
- 提现成功时自动更新月度账单
- 提现拒绝时自动返回余额到用户账户

## 部署步骤

### 1. 数据库部署

#### 1.1 执行基础表创建
```sql
-- 执行基础支付表
source sql/task_payment.sql;
```

#### 1.2 执行佣金系统表创建
```sql
-- 执行佣金系统表
source sql/commission_system.sql;
```

#### 1.3 执行菜单权限配置
```sql
-- 执行后台菜单权限配置
source sql/commission_admin_menu.sql;
```

### 2. 后端API部署

#### 2.1 新增的控制器文件
- `UserBalanceManageController.java` - 用户余额管理
- `CommissionBillManageController.java` - 佣金账单管理  
- `WithdrawManageController.java` - 提现管理
- `BalanceRecordManageController.java` - 余额变动记录管理
- `CommissionStatisticsController.java` - 佣金统计

#### 2.2 新增的服务层文件
- `IUserBalanceService.java` + `UserBalanceServiceImpl.java`
- `ICommissionBillService.java` + `CommissionBillServiceImpl.java`
- `IBalanceRecordService.java` + `BalanceRecordServiceImpl.java`

#### 2.3 新增的数据访问层文件
- `UserBalanceMapper.java` + `UserBalanceMapper.xml`
- `CommissionBillMapper.java` + `CommissionBillMapper.xml`
- `BalanceRecordMapper.java` + `BalanceRecordMapper.xml`

#### 2.4 新增的实体类文件
- `UserBalance.java` - 用户余额实体
- `CommissionBill.java` - 佣金账单实体
- `BalanceRecord.java` - 余额变动记录实体

#### 2.5 修改的现有文件
- `AppTaskServiceImpl.java` - 添加任务完成佣金分配逻辑
- `WithdrawServiceImpl.java` - 添加提现拒绝返回佣金逻辑
- `WithdrawRecord.java` - 添加渠道和状态中文名称方法

### 3. 前端页面部署

#### 3.1 新增的API文件
```
fuguang-web/src/api/fuguang/
├── balance.js          # 用户余额API
├── withdraw.js         # 提现管理API
└── statistics.js       # 统计数据API
```

#### 3.2 新增的页面文件
```
fuguang-web/src/views/fuguang/
├── balance/
│   └── index.vue       # 用户余额管理页面
├── withdraw/
│   └── index.vue       # 提现管理页面
└── statistics/
    └── index.vue       # 佣金统计页面
```

### 4. 权限配置

#### 4.1 菜单结构
```
佣金管理
├── 用户余额 (fuguang:balance:list)
├── 佣金账单 (fuguang:commission:list)
├── 提现管理 (fuguang:withdraw:list)
├── 余额记录 (fuguang:balanceRecord:list)
└── 佣金统计 (fuguang:statistics:overview)
```

#### 4.2 权限标识
- `fuguang:balance:*` - 用户余额相关权限
- `fuguang:commission:*` - 佣金账单相关权限
- `fuguang:withdraw:*` - 提现管理相关权限
- `fuguang:balanceRecord:*` - 余额记录相关权限
- `fuguang:statistics:*` - 统计数据相关权限

## API接口说明

### 用户余额管理接口
- `GET /fuguang/balance/list` - 查询用户余额列表
- `GET /fuguang/balance/{balanceId}` - 查询用户余额详情
- `POST /fuguang/balance/adjust` - 手动调整用户余额
- `POST /fuguang/balance/freeze` - 冻结用户余额
- `POST /fuguang/balance/unfreeze` - 解冻用户余额
- `GET /fuguang/balance/statistics` - 获取余额统计信息

### 提现管理接口
- `GET /fuguang/withdraw/list` - 查询提现记录列表
- `GET /fuguang/withdraw/{withdrawId}` - 查询提现记录详情
- `POST /fuguang/withdraw/audit` - 审核提现申请
- `POST /fuguang/withdraw/batchAudit` - 批量审核提现申请
- `GET /fuguang/withdraw/pendingCount` - 获取待审核提现数量
- `GET /fuguang/withdraw/statistics` - 获取提现统计信息

### 佣金统计接口
- `GET /fuguang/statistics/overview` - 获取系统概览
- `GET /fuguang/statistics/trend` - 获取收支趋势
- `GET /fuguang/statistics/balance-distribution` - 获取余额分布
- `GET /fuguang/statistics/income-distribution` - 获取收入分布
- `GET /fuguang/statistics/withdraw-channel` - 获取提现渠道分布

## 业务流程

### 任务完成佣金分配流程
1. 用户完成任务
2. 系统调用 `AppTaskServiceImpl.completeTask()` 方法
3. 自动初始化用户余额（如果不存在）
4. 增加用户余额（任务佣金）
5. 更新月度佣金账单
6. 记录余额变动

### 提现审核流程
1. 用户申请提现
2. 管理员在后台查看待审核提现
3. 审核通过：调用支付宝转账接口，更新提现状态，更新月度账单
4. 审核拒绝：更新提现状态为失败，返回金额到用户余额

### 余额调整流程
1. 管理员选择用户进行余额调整
2. 选择调整类型（增加/减少）和金额
3. 填写调整原因
4. 系统更新用户余额并记录变动

## 注意事项

### 数据一致性
- 所有涉及金额的操作都使用事务保证数据一致性
- 余额变动和账单更新同步进行
- 提现失败时确保金额正确返回用户账户

### 安全性
- 所有金额计算使用 BigDecimal 确保精度
- 余额操作前检查用户余额是否充足
- 提现审核需要管理员权限
- 详细的操作日志记录

### 性能优化
- 数据库表添加了合适的索引
- 统计查询使用缓存机制
- 分页查询避免大数据量加载

## 测试建议

### 功能测试
1. 测试任务完成后佣金自动分配
2. 测试提现申请审核流程
3. 测试余额调整功能
4. 测试统计数据准确性

### 边界测试
1. 测试余额不足时的操作限制
2. 测试大金额的精度计算
3. 测试并发操作的数据一致性

### 性能测试
1. 测试大量用户数据的查询性能
2. 测试统计图表的加载速度
3. 测试批量操作的处理能力

## 维护说明

### 日志监控
- 关注佣金分配的成功率
- 监控提现处理的异常情况
- 定期检查数据一致性

### 数据备份
- 定期备份用户余额数据
- 备份重要的变动记录
- 保留提现记录的完整性

### 系统升级
- 升级前备份相关数据表
- 测试环境验证功能完整性
- 生产环境分步骤部署
