package com.ruoyi.app.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.fuguang.domain.MerchantApplication;
import com.ruoyi.fuguang.service.IMerchantApplicationService;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.config.RuoYiConfig;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.bind.annotation.RequestParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * APP商家申请Controller
 * 
 * <AUTHOR>
 * @date 2025-01-22
 */
@Api(tags = "APP商家申请管理")
@RestController("merchantApplicationApiController")
@RequestMapping("/app/merchant")
public class MerchantApplicationController extends BaseController
{
    @Autowired
    private IMerchantApplicationService merchantApplicationService;

    /**
     * 查询当前用户的商家申请
     */
    @ApiOperation("查询当前用户的商家申请")
    @GetMapping("/application")
    public AjaxResult getMyApplication()
    {
        Long userId = getUserId();
        MerchantApplication application = merchantApplicationService.selectMerchantApplicationByUserId(userId);
        return success(application);
    }

    /**
     * 检查用户是否可以申请成为商家
     */
    @ApiOperation("检查用户是否可以申请成为商家")
    @GetMapping("/checkCanApply")
    public AjaxResult checkCanApply()
    {
        Long userId = getUserId();
        boolean canApply = merchantApplicationService.checkUserCanApply(userId);
        return success(canApply);
    }

    /**
     * 提交商家申请
     */
    @ApiOperation("提交商家申请")
    @Log(title = "商家申请", businessType = BusinessType.INSERT)
    @PostMapping("/apply")
    public AjaxResult submitApplication(@RequestBody MerchantApplication merchantApplication)
    {
        Long userId = getUserId();
        merchantApplication.setUserId(userId);
        
        // 检查用户是否可以申请
        if (!merchantApplicationService.checkUserCanApply(userId))
        {
            return error("您已经是商家用户或已有待审核的申请，无法重复申请");
        }
        
        // 数据校验
        String validateResult = merchantApplicationService.validateApplicationData(merchantApplication);
        if (StringUtils.isNotEmpty(validateResult))
        {
            return error(validateResult);
        }
        
        merchantApplication.setCreateBy(String.valueOf(userId));
        boolean result = merchantApplicationService.submitApplication(merchantApplication);
        
        if (result)
        {
            return success("申请提交成功，请等待审核");
        }
        return error("申请提交失败");
    }

    /**
     * 修改商家申请（仅限待审核状态）
     */
    @ApiOperation("修改商家申请")
    @Log(title = "商家申请", businessType = BusinessType.UPDATE)
    @PutMapping("/application")
    public AjaxResult updateApplication(@RequestBody MerchantApplication merchantApplication)
    {
        Long userId = getUserId();
        
        // 检查申请是否存在且属于当前用户
        MerchantApplication existApplication = merchantApplicationService.selectMerchantApplicationByUserId(userId);
        if (existApplication == null)
        {
            return error("申请不存在");
        }
        
        // 只有待审核状态的申请才能修改
        if (!"0".equals(existApplication.getApplicationStatus()))
        {
            return error("只有待审核状态的申请才能修改");
        }
        
        merchantApplication.setApplicationId(existApplication.getApplicationId());
        merchantApplication.setUserId(userId);
        
        // 数据校验
        String validateResult = merchantApplicationService.validateApplicationData(merchantApplication);
        if (StringUtils.isNotEmpty(validateResult))
        {
            return error(validateResult);
        }
        
        merchantApplication.setUpdateBy(String.valueOf(userId));
        int result = merchantApplicationService.updateMerchantApplication(merchantApplication);
        
        if (result > 0)
        {
            return success("申请修改成功");
        }
        return error("申请修改失败");
    }

    /**
     * 撤销商家申请（仅限待审核状态）
     */
    @ApiOperation("撤销商家申请")
    @Log(title = "商家申请", businessType = BusinessType.DELETE)
    @DeleteMapping("/application")
    public AjaxResult cancelApplication()
    {
        Long userId = getUserId();
        
        // 检查申请是否存在且属于当前用户
        MerchantApplication existApplication = merchantApplicationService.selectMerchantApplicationByUserId(userId);
        if (existApplication == null)
        {
            return error("申请不存在");
        }
        
        // 只有待审核状态的申请才能撤销
        if (!"0".equals(existApplication.getApplicationStatus()))
        {
            return error("只有待审核状态的申请才能撤销");
        }
        
        int result = merchantApplicationService.deleteMerchantApplicationByApplicationId(existApplication.getApplicationId());
        
        if (result > 0)
        {
            return success("申请撤销成功");
        }
        return error("申请撤销失败");
    }

    /**
     * 获取申请状态说明
     */
    @ApiOperation("获取申请状态说明")
    @GetMapping("/statusInfo")
    public AjaxResult getStatusInfo()
    {
        return success("申请状态说明", new String[]{
            "0: 待审核 - 您的申请已提交，请耐心等待管理员审核",
            "1: 审核通过 - 恭喜您成为商家用户，可以开始发布商品",
            "2: 审核拒绝 - 很抱歉，您的申请未通过审核，请查看审核意见后重新申请"
        });
    }
}
