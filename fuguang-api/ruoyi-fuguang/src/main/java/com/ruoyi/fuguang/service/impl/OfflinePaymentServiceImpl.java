package com.ruoyi.fuguang.service.impl;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.fuguang.domain.MerchantApplication;
import com.ruoyi.fuguang.domain.MerchantQrcode;
import com.ruoyi.fuguang.domain.OfflinePayment;
import com.ruoyi.fuguang.mapper.MerchantQrcodeMapper;
import com.ruoyi.fuguang.mapper.OfflinePaymentMapper;
import com.ruoyi.fuguang.service.IAlipayService;
import com.ruoyi.fuguang.service.IMerchantApplicationService;
import com.ruoyi.fuguang.service.IOfflinePaymentService;

/**
 * 线下支付Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@Service
public class OfflinePaymentServiceImpl implements IOfflinePaymentService 
{
    private static final Logger log = LoggerFactory.getLogger(OfflinePaymentServiceImpl.class);

    @Autowired
    private OfflinePaymentMapper offlinePaymentMapper;

    @Autowired
    private MerchantQrcodeMapper merchantQrcodeMapper;

    @Autowired
    private IMerchantApplicationService merchantApplicationService;

    @Autowired
    private IAlipayService alipayService;

    /**
     * 查询线下支付订单
     * 
     * @param paymentId 线下支付订单主键
     * @return 线下支付订单
     */
    @Override
    public OfflinePayment selectOfflinePaymentByPaymentId(Long paymentId)
    {
        return offlinePaymentMapper.selectOfflinePaymentByPaymentId(paymentId);
    }

    /**
     * 根据订单号查询线下支付订单
     * 
     * @param orderNo 订单号
     * @return 线下支付订单
     */
    @Override
    public OfflinePayment selectOfflinePaymentByOrderNo(String orderNo)
    {
        return offlinePaymentMapper.selectOfflinePaymentByOrderNo(orderNo);
    }

    /**
     * 查询线下支付订单列表
     * 
     * @param offlinePayment 线下支付订单
     * @return 线下支付订单
     */
    @Override
    public List<OfflinePayment> selectOfflinePaymentList(OfflinePayment offlinePayment)
    {
        return offlinePaymentMapper.selectOfflinePaymentList(offlinePayment);
    }

    /**
     * 新增线下支付订单
     * 
     * @param offlinePayment 线下支付订单
     * @return 结果
     */
    @Override
    public int insertOfflinePayment(OfflinePayment offlinePayment)
    {
        offlinePayment.setCreateTime(DateUtils.getNowDate());
        return offlinePaymentMapper.insertOfflinePayment(offlinePayment);
    }

    /**
     * 修改线下支付订单
     * 
     * @param offlinePayment 线下支付订单
     * @return 结果
     */
    @Override
    public int updateOfflinePayment(OfflinePayment offlinePayment)
    {
        offlinePayment.setUpdateTime(DateUtils.getNowDate());
        return offlinePaymentMapper.updateOfflinePayment(offlinePayment);
    }

    /**
     * 批量删除线下支付订单
     * 
     * @param paymentIds 需要删除的线下支付订单主键
     * @return 结果
     */
    @Override
    public int deleteOfflinePaymentByPaymentIds(Long[] paymentIds)
    {
        return offlinePaymentMapper.deleteOfflinePaymentByPaymentIds(paymentIds);
    }

    /**
     * 删除线下支付订单信息
     * 
     * @param paymentId 线下支付订单主键
     * @return 结果
     */
    @Override
    public int deleteOfflinePaymentByPaymentId(Long paymentId)
    {
        return offlinePaymentMapper.deleteOfflinePaymentByPaymentId(paymentId);
    }

    /**
     * 生成商家二维码
     * 
     * @param merchantId 商家ID
     * @return 二维码信息
     */
    @Override
    public Map<String, Object> generateMerchantQrcode(Long merchantId)
    {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 查询商家信息
            MerchantApplication merchant = merchantApplicationService.selectMerchantApplicationByApplicationId(merchantId);
            if (merchant == null) {
                result.put("success", false);
                result.put("message", "商家不存在");
                return result;
            }
            
            if (!"1".equals(merchant.getApplicationStatus())) {
                result.put("success", false);
                result.put("message", "商家未通过审核");
                return result;
            }
            
            // 检查是否已存在二维码
            MerchantQrcode existingQrcode = merchantQrcodeMapper.selectMerchantQrcodeByMerchantId(merchantId);
            if (existingQrcode != null) {
                result.put("success", true);
                result.put("qrcode", existingQrcode);
                return result;
            }
            
            // 生成二维码内容（包含商家ID和基础URL）
            String baseUrl = "https://your-domain.com/app/offline-pay";
            String qrcodeContent = baseUrl + "?merchantId=" + merchantId;
            
            // 创建二维码记录
            MerchantQrcode qrcode = new MerchantQrcode();
            qrcode.setMerchantId(merchantId);
            qrcode.setMerchantName(merchant.getShopName());
            qrcode.setQrcodeContent(qrcodeContent);
            qrcode.setStatus("0");
            qrcode.setCreateTime(DateUtils.getNowDate());
            
            merchantQrcodeMapper.insertMerchantQrcode(qrcode);
            
            result.put("success", true);
            result.put("qrcode", qrcode);
            log.info("商家二维码生成成功，商家ID：{}", merchantId);
            
        } catch (Exception e) {
            log.error("生成商家二维码异常，商家ID：{}", merchantId, e);
            result.put("success", false);
            result.put("message", "生成二维码失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取商家二维码
     * 
     * @param merchantId 商家ID
     * @return 二维码信息
     */
    @Override
    public MerchantQrcode getMerchantQrcode(Long merchantId)
    {
        return merchantQrcodeMapper.selectMerchantQrcodeByMerchantId(merchantId);
    }

    /**
     * 创建线下支付订单
     * 
     * @param merchantId 商家ID
     * @param payAmount 支付金额
     * @return 支付订单信息
     */
    @Override
    @Transactional
    public Map<String, Object> createOfflinePayOrder(Long merchantId, BigDecimal payAmount)
    {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 查询商家信息
            MerchantApplication merchant = merchantApplicationService.selectMerchantApplicationByApplicationId(merchantId);
            if (merchant == null) {
                result.put("success", false);
                result.put("message", "商家不存在");
                return result;
            }
            
            if (!"1".equals(merchant.getApplicationStatus())) {
                result.put("success", false);
                result.put("message", "商家未通过审核");
                return result;
            }
            
            // 生成订单号
            String orderNo = "OFF" + System.currentTimeMillis() + String.format("%04d", (int)(Math.random() * 10000));
            
            // 创建线下支付订单
            OfflinePayment payment = new OfflinePayment();
            payment.setMerchantId(merchantId);
            payment.setMerchantName(merchant.getShopName());
            payment.setOrderNo(orderNo);
            payment.setPayAmount(payAmount);
            payment.setPayType("1"); // 支付宝
            payment.setPayStatus("0"); // 待支付
            payment.setTransferStatus("0"); // 未转账
            payment.setPlatformFee(payAmount.multiply(new BigDecimal("0.006"))); // 0.6%手续费
            payment.setTransferAmount(payAmount.subtract(payment.getPlatformFee()));
            payment.setCreateTime(DateUtils.getNowDate());
            
            insertOfflinePayment(payment);
            
            // 创建支付宝支付订单
            String subject = "线下支付-" + merchant.getShopName();
            String body = "商家：" + merchant.getShopName() + "，金额：" + payAmount + "元";
            String notifyUrl = "https://your-domain.com/app/payment/offline/alipay/notify";
            
            Map<String, Object> alipayResult = alipayService.createAppPayOrder(orderNo, payAmount, subject, body, notifyUrl);
            
            if ((Boolean) alipayResult.get("success")) {
                result.put("success", true);
                result.put("orderNo", orderNo);
                result.put("payAmount", payAmount);
                result.put("merchantName", merchant.getShopName());
                result.put("orderString", alipayResult.get("orderString"));
                log.info("线下支付订单创建成功，订单号：{}", orderNo);
            } else {
                result.put("success", false);
                result.put("message", "创建支付订单失败：" + alipayResult.get("errorMsg"));
            }
            
        } catch (Exception e) {
            log.error("创建线下支付订单异常，商家ID：{}，金额：{}", merchantId, payAmount, e);
            result.put("success", false);
            result.put("message", "创建支付订单失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 处理支付宝支付回调
     *
     * @param params 回调参数
     * @return 处理结果
     */
    @Override
    @Transactional
    public boolean handleAlipayCallback(Map<String, String> params)
    {
        try {
            String orderNo = params.get("out_trade_no");
            String tradeNo = params.get("trade_no");
            String tradeStatus = params.get("trade_status");

            log.info("处理线下支付支付宝回调，订单号：{}，交易号：{}，状态：{}", orderNo, tradeNo, tradeStatus);

            // 验证签名
            boolean signVerified = alipayService.verifyCallback(params);
            if (!signVerified) {
                log.error("支付宝回调签名验证失败，订单号：{}", orderNo);
                return false;
            }

            // 查询支付订单
            OfflinePayment payment = selectOfflinePaymentByOrderNo(orderNo);
            if (payment == null) {
                log.error("线下支付订单不存在，订单号：{}", orderNo);
                return false;
            }

            // 处理支付成功
            if ("TRADE_SUCCESS".equals(tradeStatus) && "0".equals(payment.getPayStatus())) {
                payment.setPayStatus("1");
                payment.setTradeNo(tradeNo);
                payment.setPayTime(DateUtils.getNowDate());
                payment.setNotifyTime(DateUtils.getNowDate());
                updateOfflinePayment(payment);

                // 异步转账给商家
                transferToMerchant(payment.getPaymentId());

                log.info("线下支付订单支付成功，订单号：{}", orderNo);
                return true;
            }

            return true;
        } catch (Exception e) {
            log.error("处理线下支付支付宝回调异常", e);
            return false;
        }
    }

    /**
     * 支付成功后处理
     *
     * @param orderNo 订单号
     * @param tradeNo 第三方交易号
     * @return 处理结果
     */
    @Override
    @Transactional
    public int paymentSuccess(String orderNo, String tradeNo)
    {
        try {
            OfflinePayment payment = selectOfflinePaymentByOrderNo(orderNo);
            if (payment == null) {
                log.error("线下支付订单不存在，订单号：{}", orderNo);
                return 0;
            }

            if (!"0".equals(payment.getPayStatus())) {
                log.warn("线下支付订单状态异常，订单号：{}，当前状态：{}", orderNo, payment.getPayStatus());
                return 0;
            }

            payment.setPayStatus("1");
            payment.setTradeNo(tradeNo);
            payment.setPayTime(DateUtils.getNowDate());
            payment.setUpdateTime(DateUtils.getNowDate());

            int result = updateOfflinePayment(payment);

            if (result > 0) {
                // 异步转账给商家
                transferToMerchant(payment.getPaymentId());
                log.info("线下支付订单支付成功处理完成，订单号：{}", orderNo);
            }

            return result;
        } catch (Exception e) {
            log.error("线下支付订单支付成功处理异常，订单号：{}", orderNo, e);
            return 0;
        }
    }

    /**
     * 转账给商家
     *
     * @param paymentId 支付记录ID
     * @return 转账结果
     */
    @Override
    @Transactional
    public Map<String, Object> transferToMerchant(Long paymentId)
    {
        Map<String, Object> result = new HashMap<>();

        try {
            OfflinePayment payment = selectOfflinePaymentByPaymentId(paymentId);
            if (payment == null) {
                result.put("success", false);
                result.put("message", "支付记录不存在");
                return result;
            }

            if (!"1".equals(payment.getPayStatus())) {
                result.put("success", false);
                result.put("message", "订单未支付成功");
                return result;
            }

            if (!"0".equals(payment.getTransferStatus())) {
                result.put("success", false);
                result.put("message", "已处理转账");
                return result;
            }

            // 查询商家信息
            MerchantApplication merchant = merchantApplicationService.selectMerchantApplicationByApplicationId(payment.getMerchantId());
            if (merchant == null || StringUtils.isEmpty(merchant.getAlipayAccount())) {
                result.put("success", false);
                result.put("message", "商家支付宝账号信息不完整");
                return result;
            }

            // 更新转账状态为转账中
            payment.setTransferStatus("1");
            payment.setUpdateTime(DateUtils.getNowDate());
            updateOfflinePayment(payment);

            // 生成转账单号
            String transferNo = "TF" + System.currentTimeMillis();

            // 调用支付宝转账接口
            String remark = "线下支付分账-订单号:" + payment.getOrderNo();
            Map<String, Object> transferResult = alipayService.transfer(
                transferNo,
                merchant.getAlipayAccount(),
                payment.getTransferAmount(),
                merchant.getAlipayName(),
                remark
            );

            if ((Boolean) transferResult.get("success")) {
                // 转账成功
                payment.setTransferStatus("2");
                payment.setTransferNo(transferNo);
                payment.setTransferTime(DateUtils.getNowDate());
                payment.setUpdateTime(DateUtils.getNowDate());
                updateOfflinePayment(payment);

                result.put("success", true);
                result.put("message", "转账成功");
                log.info("线下支付转账成功，支付记录ID：{}，转账单号：{}", paymentId, transferNo);
            } else {
                // 转账失败
                payment.setTransferStatus("3");
                payment.setUpdateTime(DateUtils.getNowDate());
                updateOfflinePayment(payment);

                result.put("success", false);
                result.put("message", "转账失败：" + transferResult.get("errorMsg"));
                log.error("线下支付转账失败，支付记录ID：{}，错误：{}", paymentId, transferResult.get("errorMsg"));
            }

        } catch (Exception e) {
            log.error("线下支付转账异常，支付记录ID：{}", paymentId, e);
            result.put("success", false);
            result.put("message", "转账异常：" + e.getMessage());
        }

        return result;
    }

    /**
     * 查询支付状态
     *
     * @param orderNo 订单号
     * @return 支付状态
     */
    @Override
    public String getPaymentStatus(String orderNo)
    {
        try {
            OfflinePayment payment = selectOfflinePaymentByOrderNo(orderNo);
            if (payment == null) {
                return "0"; // 订单不存在，返回待支付
            }
            return payment.getPayStatus();
        } catch (Exception e) {
            log.error("查询线下支付状态异常，订单号：{}", orderNo, e);
            return "0";
        }
    }
}
