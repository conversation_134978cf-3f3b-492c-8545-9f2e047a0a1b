# 浮光壁垒支付宝支付和提现功能说明

## 功能概述

项目已集成支付宝支付和提现功能，支持：

1. **任务发布支付**：用户发布任务时需要支付任务佣金
2. **支付宝 APP 支付**：支持移动端支付宝 APP 支付
3. **支付宝 H5 支付**：支持网页端支付宝支付
4. **支付宝提现**：支持用户将余额提现到支付宝账户
5. **支付回调处理**：自动处理支付宝异步通知
6. **支付状态查询**：实时查询支付和提现状态
7. **佣金账单系统**：完整的佣金收入和支出管理系统
8. **余额管理**：用户余额实时管理和变动记录

## 核心组件

### 1. 通用支付宝服务 (AlipayService)

- 位置：`com.ruoyi.fuguang.service.IAlipayService`
- 功能：封装支付宝 SDK，提供支付、提现、查询等通用功能

### 2. 任务支付服务 (TaskPaymentService)

- 位置：`com.ruoyi.fuguang.service.ITaskPaymentService`
- 功能：处理任务发布支付相关业务逻辑

### 3. 提现服务 (WithdrawService)

- 位置：`com.ruoyi.fuguang.service.IWithdrawService`
- 功能：处理用户提现申请和审核

### 4. 用户余额服务 (UserBalanceService)

- 位置：`com.ruoyi.fuguang.service.IUserBalanceService`
- 功能：管理用户余额、余额变动记录

### 5. 佣金账单服务 (CommissionBillService)

- 位置：`com.ruoyi.fuguang.service.ICommissionBillService`
- 功能：管理用户月度佣金账单、收入支出统计

### 6. 余额变动记录服务 (BalanceRecordService)

- 位置：`com.ruoyi.fuguang.service.IBalanceRecordService`
- 功能：记录和查询用户余额变动历史

## 数据库表

### 任务支付记录表 (task_payment)

记录任务发布时的支付信息

### 提现记录表 (withdraw_record)

记录用户提现申请和处理状态

### 用户余额表 (user_balance)

记录用户的余额信息，包括总余额、可用余额、冻结余额等

### 余额变动记录表 (balance_record)

记录用户余额的每一次变动，包括收入、支出的详细信息

### 佣金账单表 (commission_bill)

按月统计用户的佣金收入和支出情况

## API 接口

### 任务支付相关接口

1. **任务发布** - `POST /app/task`

   - 发布任务成功后自动创建支付订单
   - 返回支付参数供前端调用支付宝 SDK

2. **支付宝支付回调** - `POST /app/task/payment/alipay/notify`

   - 处理支付宝异步通知
   - 更新支付状态

3. **查询支付状态** - `GET /app/task/payment/alipay/query/{orderNo}`

   - 查询订单支付状态

4. **查询我的支付记录** - `GET /app/task/payment/my-payments`
   - 查询用户的支付记录列表

### 提现相关接口

1. **申请提现** - `POST /app/task/payment/withdraw/apply`

   - 参数：提现金额、提现方式、收款账户、收款人姓名
   - 自动计算手续费

2. **查询提现记录** - `GET /app/task/payment/withdraw/my-records`

   - 查询用户的提现记录列表

3. **查询提现状态** - `GET /app/task/payment/withdraw/status/{withdrawNo}`

   - 查询提现单状态

4. **计算提现手续费** - `GET /app/task/payment/withdraw/calculate-fee`
   - 根据提现金额和方式计算手续费

### 佣金账单相关接口

1. **获取用户余额信息** - `GET /app/commission/balance`

   - 查询用户当前余额、累计收入、累计提现等信息

2. **查询佣金账单列表** - `GET /app/commission/bills`

   - 按月查询用户的佣金账单列表

3. **获取指定月份账单详情** - `GET /app/commission/bill/{year}/{month}`

   - 查询指定年月的详细账单信息和余额变动记录

4. **获取当前月份账单详情** - `GET /app/commission/bill/current`

   - 查询当前月份的账单详情

5. **查询余额变动记录** - `GET /app/commission/records`

   - 查询用户的余额变动历史记录
   - 支持按变动类型、收入类型筛选

6. **查询提现记录** - `GET /app/commission/withdraws`

   - 查询用户的提现记录，包含提现渠道信息

7. **获取收入统计** - `GET /app/commission/income-statistics`

   - 按月统计各类收入（任务佣金、推荐奖励、其他收入）

8. **获取支出统计** - `GET /app/commission/expense-statistics`

   - 按月统计提现支出情况

9. **查询提现记录详情** - `GET /app/commission/withdraw/{withdrawId}`
   - 查询单条提现记录的详细信息，包含渠道名称

## 配置说明

在 `application.yml` 中配置支付宝参数：

```yaml
# 支付宝配置
alipay:
  # 应用ID（请替换为实际的应用ID）
  appId: 2021000000000000
  # 商户私钥（请替换为实际的私钥）
  privateKey: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...
  # 支付宝公钥（请替换为实际的公钥）
  publicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
  # 服务器异步通知页面路径
  notifyUrl: http://your-domain.com/app/payment/alipay/notify
  # 页面跳转同步通知页面路径
  returnUrl: http://your-domain.com/app/payment/alipay/return
  # 签名方式
  signType: RSA2
  # 字符编码格式
  charset: UTF-8
  # 支付宝网关（沙箱环境）
  gatewayUrl: https://openapi.alipaydev.com/gateway.do
```

## 使用流程

### 任务发布支付流程

1. 用户调用任务发布接口 `POST /app/task`
2. 系统创建任务记录
3. 系统自动创建支付订单
4. 返回支付参数给前端
5. 前端调用支付宝 SDK 发起支付
6. 支付完成后支付宝回调通知接口
7. 系统更新支付状态

### 提现流程

1. 用户调用申请提现接口 `POST /app/task/payment/withdraw/apply`
2. 系统创建提现记录，状态为"申请中"
3. 管理员审核提现申请
4. 审核通过后系统调用支付宝转账接口
5. 转账成功后更新提现状态为"提现成功"

## 手续费说明

- 支付宝提现：0.6%，最低 1 元
- 微信提现：0.6%，最低 1 元
- 银行卡提现：1%，最低 1 元
- 最小提现金额：10 元

## 部署注意事项

1. 需要先执行 `sql/task_payment.sql` 创建相关数据库表
2. 执行 `sql/commission_system.sql` 创建佣金账单系统相关表
3. 配置正确的支付宝应用参数
4. 确保服务器可以接收支付宝回调通知
5. 生产环境需要使用正式的支付宝网关地址

## 测试说明

1. 开发环境使用支付宝沙箱环境进行测试
2. 需要下载支付宝沙箱版 APP 进行支付测试
3. 提现功能需要配置支付宝商户转账权限

## 佣金账单系统特性

1. **自动佣金分配**：任务完成时自动将佣金分配给接收者
2. **实时余额管理**：用户余额实时更新，支持冻结和解冻
3. **详细变动记录**：记录每一笔余额变动的详细信息
4. **按月账单统计**：自动按月生成佣金收支账单
5. **多种收入类型**：支持任务佣金、推荐奖励、其他收入等分类
6. **提现渠道显示**：提现记录中显示具体的提现渠道信息
7. **数据一致性**：使用事务确保余额和账单数据的一致性

## 安全说明

1. 支付宝私钥和公钥需要妥善保管
2. 回调接口会验证支付宝签名确保安全性
3. 提现申请需要管理员审核后才能执行转账
4. 所有金额计算使用 BigDecimal 确保精度
5. 余额变动操作使用事务保证数据一致性
6. 佣金分配过程有详细的日志记录
