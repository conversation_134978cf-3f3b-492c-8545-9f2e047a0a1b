package com.ruoyi.fuguang.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.fuguang.domain.MerchantQrcode;
import com.ruoyi.fuguang.service.IOfflinePaymentService;
import com.ruoyi.fuguang.mapper.MerchantQrcodeMapper;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 商家二维码管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@RestController
@RequestMapping("/fuguang/merchantQrcode")
public class MerchantQrcodeController extends BaseController
{
    @Autowired
    private MerchantQrcodeMapper merchantQrcodeMapper;

    @Autowired
    private IOfflinePaymentService offlinePaymentService;

    /**
     * 查询商家二维码列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:merchantQrcode:list')")
    @GetMapping("/list")
    public TableDataInfo list(MerchantQrcode merchantQrcode)
    {
        startPage();
        List<MerchantQrcode> list = merchantQrcodeMapper.selectMerchantQrcodeList(merchantQrcode);
        return getDataTable(list);
    }

    /**
     * 导出商家二维码列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:merchantQrcode:export')")
    @Log(title = "商家二维码", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MerchantQrcode merchantQrcode)
    {
        List<MerchantQrcode> list = merchantQrcodeMapper.selectMerchantQrcodeList(merchantQrcode);
        ExcelUtil<MerchantQrcode> util = new ExcelUtil<MerchantQrcode>(MerchantQrcode.class);
        util.exportExcel(response, list, "商家二维码数据");
    }

    /**
     * 获取商家二维码详细信息
     */
    @PreAuthorize("@ss.hasPermi('fuguang:merchantQrcode:query')")
    @GetMapping(value = "/{qrcodeId}")
    public AjaxResult getInfo(@PathVariable("qrcodeId") Long qrcodeId)
    {
        return success(merchantQrcodeMapper.selectMerchantQrcodeByQrcodeId(qrcodeId));
    }

    /**
     * 新增商家二维码
     */
    @PreAuthorize("@ss.hasPermi('fuguang:merchantQrcode:add')")
    @Log(title = "商家二维码", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MerchantQrcode merchantQrcode)
    {
        return toAjax(merchantQrcodeMapper.insertMerchantQrcode(merchantQrcode));
    }

    /**
     * 修改商家二维码
     */
    @PreAuthorize("@ss.hasPermi('fuguang:merchantQrcode:edit')")
    @Log(title = "商家二维码", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MerchantQrcode merchantQrcode)
    {
        return toAjax(merchantQrcodeMapper.updateMerchantQrcode(merchantQrcode));
    }

    /**
     * 删除商家二维码
     */
    @PreAuthorize("@ss.hasPermi('fuguang:merchantQrcode:remove')")
    @Log(title = "商家二维码", businessType = BusinessType.DELETE)
	@DeleteMapping("/{qrcodeIds}")
    public AjaxResult remove(@PathVariable Long[] qrcodeIds)
    {
        return toAjax(merchantQrcodeMapper.deleteMerchantQrcodeByQrcodeIds(qrcodeIds));
    }

    /**
     * 启用/禁用商家二维码
     */
    @PreAuthorize("@ss.hasPermi('fuguang:merchantQrcode:edit')")
    @Log(title = "商家二维码状态", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody MerchantQrcode merchantQrcode)
    {
        return toAjax(merchantQrcodeMapper.updateMerchantQrcode(merchantQrcode));
    }

    /**
     * 为商家生成二维码
     */
    @PreAuthorize("@ss.hasPermi('fuguang:merchantQrcode:add')")
    @Log(title = "生成商家二维码", businessType = BusinessType.INSERT)
    @PostMapping("/generate/{merchantId}")
    public AjaxResult generateQrcode(@PathVariable Long merchantId)
    {
        Map<String, Object> result = offlinePaymentService.generateMerchantQrcode(merchantId);
        
        if ((Boolean) result.get("success")) {
            return success(result.get("qrcode"));
        } else {
            return error((String) result.get("message"));
        }
    }

    /**
     * 根据商家ID查询二维码
     */
    @PreAuthorize("@ss.hasPermi('fuguang:merchantQrcode:query')")
    @GetMapping("/merchant/{merchantId}")
    public AjaxResult getByMerchantId(@PathVariable Long merchantId)
    {
        return success(merchantQrcodeMapper.selectMerchantQrcodeByMerchantId(merchantId));
    }

    /**
     * 批量启用/禁用二维码
     */
    @PreAuthorize("@ss.hasPermi('fuguang:merchantQrcode:edit')")
    @Log(title = "批量更新二维码状态", businessType = BusinessType.UPDATE)
    @PutMapping("/batchChangeStatus")
    public AjaxResult batchChangeStatus(@RequestBody Map<String, Object> params)
    {
        Long[] qrcodeIds = (Long[]) params.get("qrcodeIds");
        String status = (String) params.get("status");
        
        int successCount = 0;
        for (Long qrcodeId : qrcodeIds) {
            MerchantQrcode qrcode = new MerchantQrcode();
            qrcode.setQrcodeId(qrcodeId);
            qrcode.setStatus(status);
            if (merchantQrcodeMapper.updateMerchantQrcode(qrcode) > 0) {
                successCount++;
            }
        }
        
        return success("成功更新 " + successCount + " 个二维码状态");
    }

    /**
     * 获取二维码统计信息
     */
    @PreAuthorize("@ss.hasPermi('fuguang:merchantQrcode:list')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics()
    {
        // 查询各种状态的二维码数量
        MerchantQrcode query = new MerchantQrcode();
        
        // 正常状态二维码
        query.setStatus("0");
        int activeCount = merchantQrcodeMapper.selectMerchantQrcodeList(query).size();
        
        // 停用状态二维码
        query.setStatus("1");
        int inactiveCount = merchantQrcodeMapper.selectMerchantQrcodeList(query).size();
        
        // 总数
        query.setStatus(null);
        int totalCount = merchantQrcodeMapper.selectMerchantQrcodeList(query).size();
        
        // 构建统计结果
        return success(new Object() {
            public final int totalQrcodes = totalCount;
            public final int activeQrcodes = activeCount;
            public final int inactiveQrcodes = inactiveCount;
        });
    }
}
