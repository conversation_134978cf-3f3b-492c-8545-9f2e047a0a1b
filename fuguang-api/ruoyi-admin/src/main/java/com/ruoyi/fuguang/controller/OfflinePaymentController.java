package com.ruoyi.fuguang.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.fuguang.domain.OfflinePayment;
import com.ruoyi.fuguang.service.IOfflinePaymentService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 线下支付订单Controller
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@RestController
@RequestMapping("/fuguang/offlinePayment")
public class OfflinePaymentController extends BaseController
{
    @Autowired
    private IOfflinePaymentService offlinePaymentService;

    /**
     * 查询线下支付订单列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:offlinePayment:list')")
    @GetMapping("/list")
    public TableDataInfo list(OfflinePayment offlinePayment)
    {
        startPage();
        List<OfflinePayment> list = offlinePaymentService.selectOfflinePaymentList(offlinePayment);
        return getDataTable(list);
    }

    /**
     * 导出线下支付订单列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:offlinePayment:export')")
    @Log(title = "线下支付订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OfflinePayment offlinePayment)
    {
        List<OfflinePayment> list = offlinePaymentService.selectOfflinePaymentList(offlinePayment);
        ExcelUtil<OfflinePayment> util = new ExcelUtil<OfflinePayment>(OfflinePayment.class);
        util.exportExcel(response, list, "线下支付订单数据");
    }

    /**
     * 获取线下支付订单详细信息
     */
    @PreAuthorize("@ss.hasPermi('fuguang:offlinePayment:query')")
    @GetMapping(value = "/{paymentId}")
    public AjaxResult getInfo(@PathVariable("paymentId") Long paymentId)
    {
        return success(offlinePaymentService.selectOfflinePaymentByPaymentId(paymentId));
    }

    /**
     * 新增线下支付订单
     */
    @PreAuthorize("@ss.hasPermi('fuguang:offlinePayment:add')")
    @Log(title = "线下支付订单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OfflinePayment offlinePayment)
    {
        return toAjax(offlinePaymentService.insertOfflinePayment(offlinePayment));
    }

    /**
     * 修改线下支付订单
     */
    @PreAuthorize("@ss.hasPermi('fuguang:offlinePayment:edit')")
    @Log(title = "线下支付订单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OfflinePayment offlinePayment)
    {
        return toAjax(offlinePaymentService.updateOfflinePayment(offlinePayment));
    }

    /**
     * 删除线下支付订单
     */
    @PreAuthorize("@ss.hasPermi('fuguang:offlinePayment:remove')")
    @Log(title = "线下支付订单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{paymentIds}")
    public AjaxResult remove(@PathVariable Long[] paymentIds)
    {
        return toAjax(offlinePaymentService.deleteOfflinePaymentByPaymentIds(paymentIds));
    }

    /**
     * 重新转账给商家
     */
    @PreAuthorize("@ss.hasPermi('fuguang:offlinePayment:transfer')")
    @Log(title = "线下支付转账", businessType = BusinessType.UPDATE)
    @PostMapping("/transfer/{paymentId}")
    public AjaxResult retryTransfer(@PathVariable Long paymentId)
    {
        Map<String, Object> result = offlinePaymentService.transferToMerchant(paymentId);
        
        if ((Boolean) result.get("success")) {
            return success(result.get("message"));
        } else {
            return error((String) result.get("message"));
        }
    }

    /**
     * 根据订单号查询支付订单
     */
    @PreAuthorize("@ss.hasPermi('fuguang:offlinePayment:query')")
    @GetMapping("/orderNo/{orderNo}")
    public AjaxResult getByOrderNo(@PathVariable String orderNo)
    {
        return success(offlinePaymentService.selectOfflinePaymentByOrderNo(orderNo));
    }

    /**
     * 获取支付统计信息
     */
    @PreAuthorize("@ss.hasPermi('fuguang:offlinePayment:list')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics()
    {
        // 查询各种状态的订单数量
        OfflinePayment query = new OfflinePayment();
        
        // 待支付订单
        query.setPayStatus("0");
        int pendingCount = offlinePaymentService.selectOfflinePaymentList(query).size();
        
        // 支付成功订单
        query.setPayStatus("1");
        int successCount = offlinePaymentService.selectOfflinePaymentList(query).size();
        
        // 转账失败订单
        query.setPayStatus("1");
        query.setTransferStatus("3");
        int transferFailedCount = offlinePaymentService.selectOfflinePaymentList(query).size();
        
        // 构建统计结果
        return success(new Object() {
            public final int pendingPayments = pendingCount;
            public final int successPayments = successCount;
            public final int transferFailedPayments = transferFailedCount;
        });
    }
}
