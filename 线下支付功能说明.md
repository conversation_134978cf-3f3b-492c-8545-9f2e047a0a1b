# 浮光壁垒线下支付功能说明

## 功能概述

线下支付功能允许商家生成专属二维码，用户通过扫码进行线下支付，支付成功后资金先到平台账户，然后自动转账给商家。

## 核心功能

### 1. 商家二维码生成
- 商家审核通过后可生成专属二维码
- 二维码包含商家ID和支付页面链接
- 支持APP内扫码和支付宝扫码两种方式

### 2. 线下支付流程
1. 用户扫描商家二维码
2. 跳转到支付页面，输入支付金额
3. 调用支付宝支付接口
4. 支付成功后资金到平台账户
5. 系统自动转账给商家（扣除平台手续费）

### 3. 分账机制
- 平台收取0.6%手续费
- 剩余金额自动转账给商家支付宝账户
- 支持转账状态查询和重试

## 数据库表结构

### 1. 商家申请表扩展 (merchant_application)
新增字段：
- `alipay_account` - 支付宝账号
- `alipay_name` - 支付宝收款人姓名

### 2. 线下支付订单表 (offline_payment)
```sql
CREATE TABLE `offline_payment` (
  `payment_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '支付记录ID',
  `merchant_id` bigint(20) NOT NULL COMMENT '商家ID（商家申请ID）',
  `merchant_name` varchar(100) NOT NULL COMMENT '商家名称',
  `order_no` varchar(64) NOT NULL COMMENT '支付订单号',
  `pay_amount` decimal(10,2) NOT NULL COMMENT '支付金额',
  `pay_type` char(1) NOT NULL DEFAULT '1' COMMENT '支付方式（1支付宝 2微信 3余额）',
  `pay_status` char(1) NOT NULL DEFAULT '0' COMMENT '支付状态（0待支付 1支付成功 2支付失败 3已退款）',
  `trade_no` varchar(64) DEFAULT NULL COMMENT '第三方交易号',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `notify_time` datetime DEFAULT NULL COMMENT '通知时间',
  `transfer_status` char(1) DEFAULT '0' COMMENT '转账状态（0未转账 1转账中 2转账成功 3转账失败）',
  `transfer_no` varchar(64) DEFAULT NULL COMMENT '转账单号',
  `transfer_time` datetime DEFAULT NULL COMMENT '转账时间',
  `transfer_amount` decimal(10,2) DEFAULT 0.00 COMMENT '转账金额',
  `platform_fee` decimal(10,2) DEFAULT 0.00 COMMENT '平台手续费',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`payment_id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_pay_status` (`pay_status`),
  KEY `idx_transfer_status` (`transfer_status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB COMMENT='线下支付订单表';
```

### 3. 商家二维码信息表 (merchant_qrcode)
```sql
CREATE TABLE `merchant_qrcode` (
  `qrcode_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '二维码ID',
  `merchant_id` bigint(20) NOT NULL COMMENT '商家ID（商家申请ID）',
  `merchant_name` varchar(100) NOT NULL COMMENT '商家名称',
  `qrcode_content` text NOT NULL COMMENT '二维码内容',
  `qrcode_url` varchar(500) DEFAULT NULL COMMENT '二维码图片URL',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`qrcode_id`),
  UNIQUE KEY `uk_merchant_id` (`merchant_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB COMMENT='商家二维码信息表';
```

## API接口

### 1. 商家二维码相关接口

#### 生成商家二维码
- **接口**: `GET /app/payment/offline/merchant/{merchantId}/qrcode`
- **描述**: 为审核通过的商家生成专属二维码
- **参数**: merchantId - 商家ID
- **返回**: 二维码信息

#### 获取商家二维码
- **接口**: `GET /app/payment/offline/merchant/{merchantId}/qrcode/info`
- **描述**: 获取商家已生成的二维码信息
- **参数**: merchantId - 商家ID
- **返回**: 二维码信息

### 2. 线下支付相关接口

#### 创建线下支付订单
- **接口**: `POST /app/payment/offline/create`
- **描述**: 创建线下支付订单
- **参数**: 
  - merchantId - 商家ID
  - payAmount - 支付金额
- **返回**: 支付订单信息和支付宝支付参数

#### 支付宝回调处理
- **接口**: `POST /app/payment/offline/alipay/notify`
- **描述**: 处理支付宝支付回调通知
- **参数**: 支付宝回调参数
- **返回**: success/failure

#### 查询支付状态
- **接口**: `GET /app/payment/offline/query/{orderNo}`
- **描述**: 查询线下支付订单状态
- **参数**: orderNo - 订单号
- **返回**: 支付状态信息

#### 模拟支付成功（测试用）
- **接口**: `POST /app/payment/offline/test/success`
- **描述**: 模拟支付成功，仅用于测试环境
- **参数**: orderNo - 订单号
- **返回**: 处理结果

## 核心服务类

### 1. OfflinePaymentService
- **位置**: `com.ruoyi.fuguang.service.IOfflinePaymentService`
- **功能**: 
  - 生成和管理商家二维码
  - 创建线下支付订单
  - 处理支付回调
  - 执行商家分账转账

### 2. 主要方法说明

#### generateMerchantQrcode(Long merchantId)
- 为商家生成专属二维码
- 检查商家审核状态
- 避免重复生成

#### createOfflinePayOrder(Long merchantId, BigDecimal payAmount)
- 创建线下支付订单
- 计算平台手续费（0.6%）
- 调用支付宝支付接口

#### handleAlipayCallback(Map<String, String> params)
- 验证支付宝回调签名
- 更新支付状态
- 触发自动转账

#### transferToMerchant(Long paymentId)
- 向商家支付宝账户转账
- 扣除平台手续费
- 记录转账状态

## 使用流程

### 1. 商家端
1. 提交商家申请（包含支付宝账号信息）
2. 等待管理员审核通过
3. 生成专属二维码
4. 将二维码展示给用户扫码支付

### 2. 用户端
1. 扫描商家二维码
2. 跳转到支付页面
3. 输入支付金额
4. 选择支付宝支付
5. 完成支付

### 3. 系统端
1. 接收支付宝回调通知
2. 验证签名和订单信息
3. 更新支付状态
4. 自动转账给商家
5. 记录转账结果

## 配置说明

### 1. 支付宝配置
需要在 `application.yml` 中配置支付宝相关参数：
- appId: 支付宝应用ID
- privateKey: 商户私钥
- publicKey: 支付宝公钥
- notifyUrl: 回调通知地址

### 2. 回调地址配置
线下支付回调地址：`https://your-domain.com/app/payment/offline/alipay/notify`

### 3. 二维码内容格式
```
https://your-domain.com/app/offline-pay?merchantId={merchantId}
```

## 注意事项

1. **商家资质**: 只有审核通过的商家才能生成二维码
2. **支付宝账号**: 商家必须填写正确的支付宝账号和收款人姓名
3. **手续费**: 平台收取0.6%手续费，需要在商家协议中说明
4. **转账限制**: 受支付宝转账接口限制，单笔转账金额有上限
5. **安全性**: 所有回调都需要验证支付宝签名
6. **异常处理**: 转账失败时需要人工处理或重试机制

## 部署说明

1. 执行SQL脚本创建相关表：
   ```bash
   # 执行 fuguang-api/sql/ry_20250522.sql（已更新）
   # 执行 fuguang-api/sql/offline_payment.sql
   ```

2. 配置支付宝参数

3. 部署应用并测试支付流程

4. 配置域名和HTTPS证书（支付宝要求）
