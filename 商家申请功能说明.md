# 商家申请功能说明

## 功能概述

本功能实现了普通用户申请成为商家用户的完整流程，包括申请提交、管理员审核、自动升级用户类型等功能。

## 功能特性

### 1. 申请流程
- 普通用户可以申请成为商家用户
- 需要上传营业执照、填写店铺信息、法人信息等
- 支持申请状态查询和修改（仅待审核状态）
- 支持申请撤销（仅待审核状态）

### 2. 审核流程
- 管理员可以查看所有申请列表
- 支持单个审核和批量审核
- 审核通过后自动将用户类型升级为商家用户
- 支持审核备注功能

### 3. 权限控制
- 只有普通用户可以申请
- 商家用户不能重复申请
- 每个用户只能有一个有效申请（待审核或已通过）

## 数据库变更

### 新增表：merchant_application
```sql
-- 商家申请表
create table merchant_application (
  application_id    bigint(20)      not null auto_increment    comment '申请ID',
  user_id           bigint(20)      not null                   comment '用户ID',
  business_license  varchar(200)    not null                   comment '营业执照图片地址',
  shop_name         varchar(100)    not null                   comment '店铺名称',
  shop_address      varchar(200)    not null                   comment '店铺地址',
  shop_longitude    varchar(20)     default ''                 comment '店铺经度',
  shop_latitude     varchar(20)     default ''                 comment '店铺纬度',
  legal_person      varchar(50)     not null                   comment '法人姓名',
  legal_id_card     varchar(18)     not null                   comment '法人身份证号',
  legal_phone       varchar(11)     not null                   comment '法人联系方式',
  business_scope    varchar(500)    default ''                 comment '经营范围',
  application_status char(1)        default '0'                comment '申请状态（0待审核 1审核通过 2审核拒绝）',
  audit_by          varchar(64)     default ''                 comment '审核人',
  audit_time        datetime                                   comment '审核时间',
  audit_remark      varchar(500)    default ''                 comment '审核备注',
  -- 其他基础字段...
);
```

### 新增菜单权限
- 商家申请审核菜单（ID: 2020）
- 相关操作权限（查询、新增、修改、删除、导出、审核）

## API接口

### APP端接口
- `GET /app/merchant/application` - 查询当前用户申请状态
- `GET /app/merchant/checkCanApply` - 检查是否可以申请
- `POST /app/merchant/apply` - 提交商家申请
- `PUT /app/merchant/application` - 修改申请（仅待审核状态）
- `DELETE /app/merchant/application` - 撤销申请（仅待审核状态）
- `POST /app/merchant/uploadLicense` - 上传营业执照
- `GET /app/merchant/statusInfo` - 获取状态说明

### 管理后台接口
- `GET /fuguang/merchant/list` - 查询申请列表
- `GET /fuguang/merchant/{id}` - 查询申请详情
- `PUT /fuguang/merchant/audit` - 审核申请
- `PUT /fuguang/merchant/batchAudit` - 批量审核
- `GET /fuguang/merchant/pendingCount` - 获取待审核数量

## 前端页面

### 管理后台页面
- 路径：`fuguang-web/src/views/fuguang/merchant/index.vue`
- 功能：申请列表查看、详情查看、审核操作、批量操作

### 页面特性
- 支持多条件搜索
- 支持批量审核操作
- 营业执照图片预览
- 审核状态标签显示
- 导出Excel功能

## 使用流程

### 1. 用户申请流程
1. 普通用户登录APP
2. 在个人中心查看是否可以申请成为商家
3. 填写申请信息：
   - 上传营业执照
   - 填写店铺名称、地址
   - 填写法人信息（姓名、身份证、联系方式）
   - 填写经营范围（可选）
4. 提交申请，等待审核

### 2. 管理员审核流程
1. 管理员登录后台系统
2. 进入"浮光壁垒" -> "商家申请审核"菜单
3. 查看申请列表，可以按状态筛选
4. 点击"查看"查看申请详情
5. 审核操作：
   - 单个审核：点击"通过"或"拒绝"
   - 批量审核：选择多个申请，点击"批量通过"或"批量拒绝"
6. 填写审核备注（可选）
7. 确认审核

### 3. 审核结果
- **审核通过**：用户自动升级为商家用户，可以发布商品
- **审核拒绝**：用户可以查看拒绝原因，修改后重新申请

## 申请状态说明
- `0` - 待审核：申请已提交，等待管理员审核
- `1` - 审核通过：申请通过，用户已升级为商家
- `2` - 审核拒绝：申请被拒绝，可查看拒绝原因

## 数据校验
- 营业执照：必填
- 店铺名称：必填
- 店铺地址：必填
- 法人姓名：必填
- 法人身份证：必填，格式校验
- 法人联系方式：必填，手机号格式校验

## 权限说明
- `fuguang:merchant:list` - 查看申请列表
- `fuguang:merchant:query` - 查看申请详情
- `fuguang:merchant:audit` - 审核申请
- `fuguang:merchant:export` - 导出申请数据

## 部署说明

1. 执行SQL脚本创建表和菜单：
   ```bash
   # 执行 fuguang-api/sql/ry_20250522.sql 中的相关部分
   ```

2. 重启后端服务

3. 重新登录管理后台，菜单会自动加载

4. 确认权限配置正确

## 注意事项

1. 用户类型变更是不可逆的，审核通过后用户将永久成为商家用户
2. 每个用户只能有一个有效申请，重复申请会被拒绝
3. 营业执照图片需要确保清晰可见
4. 法人信息需要真实有效
5. 建议定期清理已拒绝的过期申请数据
