# 浮光壁垒线下支付管理后台功能说明

## 功能概述

管理后台提供了完整的线下支付管理功能，包括支付订单管理、商家二维码管理、转账管理和数据统计等功能。

## 主要功能模块

### 1. 线下支付管理目录
位置：系统菜单 > 浮光壁垒 > 线下支付管理

包含以下子模块：
- 支付订单管理
- 二维码管理  
- 支付统计

### 2. 支付订单管理
**路径**: `/fuguang/offlinePayment`
**权限**: `fuguang:offlinePayment:*`

#### 主要功能：
- **订单列表查询**: 支持按商家名称、订单号、支付状态、转账状态等条件筛选
- **订单详情查看**: 查看完整的订单信息，包括支付时间、转账状态等
- **订单状态管理**: 修改订单状态和备注信息
- **转账重试**: 对转账失败的订单进行重新转账
- **批量操作**: 支持批量删除和批量重试转账
- **数据导出**: 导出订单数据为Excel文件

#### 订单状态说明：
- **支付状态**:
  - 待支付 (0) - 黄色标签
  - 支付成功 (1) - 绿色标签
  - 支付失败 (2) - 红色标签
  - 已退款 (3) - 灰色标签

- **转账状态**:
  - 未转账 (0) - 灰色标签
  - 转账中 (1) - 黄色标签
  - 转账成功 (2) - 绿色标签
  - 转账失败 (3) - 红色标签

#### 特殊操作：
- 对于支付成功但转账失败的订单，会显示"重新转账"按钮
- 支持批量选择转账失败的订单进行重试

### 3. 商家二维码管理
**路径**: `/fuguang/merchantQrcode`
**权限**: `fuguang:merchantQrcode:*`

#### 主要功能：
- **二维码列表**: 显示所有商家的二维码信息
- **二维码生成**: 为审核通过的商家生成专属二维码
- **状态管理**: 启用/禁用商家二维码
- **二维码预览**: 查看二维码图片和内容
- **批量操作**: 批量启用/禁用二维码
- **数据导出**: 导出二维码数据

#### 二维码状态：
- **正常 (0)**: 二维码可正常使用
- **停用 (1)**: 二维码被禁用，无法使用

#### 操作说明：
- 只有审核通过的商家才能生成二维码
- 每个商家只能有一个二维码
- 可以随时启用/禁用二维码
- 二维码内容格式：`https://your-domain.com/app/offline-pay?merchantId={merchantId}`

### 4. 支付统计页面
**路径**: `/fuguang/offlinePayment/statistics`
**权限**: `fuguang:offlinePayment:list`

#### 统计内容：
- **订单统计卡片**:
  - 待支付订单数量
  - 支付成功订单数量
  - 转账失败订单数量
  - 活跃二维码数量

- **最近支付订单**: 显示最近的5笔支付订单
- **转账失败订单**: 显示需要处理的转账失败订单，支持直接重试
- **商家二维码统计**: 显示二维码的总数、活跃数、停用数

#### 快捷操作：
- 点击"查看更多"跳转到对应的管理页面
- 直接在统计页面重试转账失败的订单
- 快速跳转到二维码管理页面

### 5. 商家申请管理增强
在原有的商家申请管理页面中新增：
- **支付宝账号信息显示**: 在商家详情中显示支付宝账号和收款人姓名
- **生成二维码按钮**: 对于审核通过的商家，显示"生成二维码"按钮
- **一键生成**: 点击按钮即可为商家生成专属二维码

## 权限配置

### 菜单权限
```sql
-- 线下支付管理目录
fuguang:offlinePayment:*

-- 支付订单管理
fuguang:offlinePayment:list     -- 查询列表
fuguang:offlinePayment:query    -- 查看详情
fuguang:offlinePayment:add      -- 新增订单
fuguang:offlinePayment:edit     -- 修改订单
fuguang:offlinePayment:remove   -- 删除订单
fuguang:offlinePayment:export   -- 导出数据
fuguang:offlinePayment:transfer -- 重新转账

-- 商家二维码管理
fuguang:merchantQrcode:list     -- 查询列表
fuguang:merchantQrcode:query    -- 查看详情
fuguang:merchantQrcode:add      -- 新增二维码
fuguang:merchantQrcode:edit     -- 修改二维码
fuguang:merchantQrcode:remove   -- 删除二维码
fuguang:merchantQrcode:export   -- 导出数据
```

### 角色分配建议
- **系统管理员**: 拥有所有权限
- **财务管理员**: 拥有查询、导出、转账重试权限
- **客服人员**: 拥有查询、查看详情权限
- **商家管理员**: 拥有二维码管理权限

## 使用流程

### 1. 商家二维码生成流程
1. 商家提交申请（包含支付宝账号信息）
2. 管理员在"商家申请审核"中审核通过
3. 在商家列表中点击"生成二维码"按钮
4. 系统自动生成专属二维码
5. 在"二维码管理"中查看和管理二维码

### 2. 支付订单处理流程
1. 用户扫码支付后，订单自动创建
2. 支付成功后，系统自动转账给商家
3. 如转账失败，在"支付订单管理"中重试
4. 通过"支付统计"监控整体情况

### 3. 异常处理流程
1. **转账失败处理**:
   - 在统计页面查看转账失败订单
   - 点击"重试"按钮重新转账
   - 或在订单管理页面批量重试

2. **二维码异常处理**:
   - 在二维码管理页面禁用异常二维码
   - 重新生成新的二维码
   - 批量管理多个二维码状态

## 数据字典配置

系统已自动配置以下数据字典：

### 支付状态 (pay_status)
- 待支付 (0) - 黄色
- 支付成功 (1) - 绿色  
- 支付失败 (2) - 红色
- 已退款 (3) - 灰色

### 支付方式 (pay_type)
- 支付宝 (1) - 蓝色
- 微信 (2) - 绿色
- 余额 (3) - 黄色

### 转账状态 (transfer_status)
- 未转账 (0) - 灰色
- 转账中 (1) - 黄色
- 转账成功 (2) - 绿色
- 转账失败 (3) - 红色

## 注意事项

1. **权限控制**: 确保不同角色用户只能访问对应的功能
2. **数据安全**: 转账操作需要谨慎，建议设置二次确认
3. **监控告警**: 建议对转账失败订单设置告警机制
4. **定期清理**: 定期清理过期的支付订单数据
5. **备份恢复**: 重要的支付数据需要定期备份

## 技术实现

### 后端接口
- **OfflinePaymentController**: 支付订单管理接口
- **MerchantQrcodeController**: 商家二维码管理接口
- **OfflinePaymentService**: 核心业务逻辑服务

### 前端页面
- **offlinePayment/index.vue**: 支付订单管理页面
- **merchantQrcode/index.vue**: 商家二维码管理页面
- **offlinePayment/statistics.vue**: 支付统计页面

### 数据库表
- **offline_payment**: 线下支付订单表
- **merchant_qrcode**: 商家二维码信息表
- **merchant_application**: 商家申请表（已扩展）

## 部署说明

1. 执行SQL脚本更新数据库结构和菜单权限
2. 重启后端服务
3. 清理前端缓存并重新构建
4. 配置相应的角色权限
5. 测试各项功能是否正常

通过以上管理后台功能，可以全面管理线下支付业务，确保资金安全和业务正常运行。
